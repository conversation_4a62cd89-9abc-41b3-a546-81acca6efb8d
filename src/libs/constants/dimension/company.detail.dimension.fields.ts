import { DataStatusEnums } from '../../enums/DataStatusEnums';
import { DimensionFieldKeyEnums } from '../../enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldInputTypeEnums } from '../../enums/dimension/DimensionFieldInputTypeEnums';
import { DimensionFieldTypeEnums } from '../../enums/dimension/DimensionFiledDataTypeEnums';
import {
  CompFlagType,
  CompRealCapitalSelectType,
  CompWealthRank,
  EconTypeList,
  EntStatusList,
  ListingIndustry,
  NetProfitSelectType,
} from '../company.constants';
import { registrationRatioType } from '../risk.change.constants';
import { TargetInvestigationEnums } from '../../enums/dimension/FieldValueEnums';
import { PersonCaseTargetInvestigation } from '../judgement.constants';

/**
 * 关联方企业详情
 */
export const RelatedCompanyDetailDimensionFields = [
  {
    fieldName: '排查对象',
    fieldKey: DimensionFieldKeyEnums.targetInvestigation,
    dataType: DimensionFieldTypeEnums.String,
    comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [TargetInvestigationEnums.Self],
    options: PersonCaseTargetInvestigation,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    fieldOrder: 1,
  },
  {
    fieldName: '上市企业',
    fieldKey: DimensionFieldKeyEnums.companyListed,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '上市企业(listingstatuskw: F_4) 1-上市公司 2-非上市',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '非上市公司', value: 2 },
      { label: '上市公司', value: 1 },
      { label: '上市公司(非ST/*ST)', value: 3 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 6,
  },
  {
    fieldName: '上市板块',
    fieldKey: DimensionFieldKeyEnums.listedIndustry,
    dataType: DimensionFieldTypeEnums.String,
    comment: '上市板块',
    defaultCompareType: DimensionFieldCompareTypeEnums.ExceptAny,
    defaultValue: ['S_10'],
    options: ListingIndustry,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 12,
  },
];

export const CompanyDetailDimensionFields = [
  {
    fieldName: '企业实缴资本',
    fieldKey: DimensionFieldKeyEnums.realRegistrationAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '企业实缴资本 小于等于 100 万元',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
    defaultValue: [100],
    options: [{ unit: '万元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 3,
  },
  {
    fieldName: '企业状态',
    fieldKey: DimensionFieldKeyEnums.companyStatus,
    dataType: DimensionFieldTypeEnums.String,
    comment: '企业状态 注销99, 吊销90',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['99', '90'],
    options: EntStatusList,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 4,
  },
  {
    fieldName: '企业性质',
    fieldKey: DimensionFieldKeyEnums.companyEconType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '国有企业(*********)、央企、央企子公司、民营企业',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['*********'],
    options: EconTypeList,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 5,
  },
  {
    fieldName: '上市企业',
    fieldKey: DimensionFieldKeyEnums.companyListed,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '上市企业(listingstatuskw: F_4) 1-上市公司 2-非上市',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '非上市公司', value: 2 },
      { label: '上市公司', value: 1 },
      { label: '上市公司(非ST/*ST)', value: 3 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 6,
  },
  {
    fieldName: '企业标识',
    fieldKey: DimensionFieldKeyEnums.companyFlag,
    dataType: DimensionFieldTypeEnums.String,
    comment: '企业标识: SXFFS-涉嫌非法社会组织; YQDFFS-已取缔非法社会组织; FNC_JR-金融机构; FNC_BX-保险机构;FNC_ZJ-保险中介机构',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['SXFFS'],
    options: CompFlagType,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 7,
  },
  {
    fieldName: '法定代表人持股比例',
    fieldKey: DimensionFieldKeyEnums.legalRepresentHoldingRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '法人代表持股<5%',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [5],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 8,
  },
  {
    fieldName: '实缴资本筛选',
    fieldKey: DimensionFieldKeyEnums.realCapitalSelect,
    dataType: DimensionFieldTypeEnums.String,
    comment: '实缴资本 1亿-5亿, 5亿-20亿, 20亿-50亿, 50亿-100亿, 100亿以上',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [[10000, 50000]],
    options: CompRealCapitalSelectType,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 9,
  },
  // {
  //   fieldName: '经济类型',
  //   fieldKey: DimensionFieldKeyEnums.economicType,
  //   dataType: DimensionFieldTypeEnums.String,
  //   comment: '国企、央企',
  //   defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
  //   defaultValue: ['*********'],
  //   options: CompEconomicType,
  //   isArray: 0,
  //   inputType: DimensionFieldInputTypeEnums.Select,
  //   status: DataStatusEnums.Enabled,
  //   fieldOrder: 0,
  // },
  {
    fieldName: '净利润筛选',
    fieldKey: DimensionFieldKeyEnums.netProfit,
    dataType: DimensionFieldTypeEnums.String,
    comment: '净利润: 20亿-50亿,50亿-100亿,100亿以上',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [[200000, 500000]],
    options: NetProfitSelectType,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 10,
  },
  {
    fieldName: '财富榜单',
    fieldKey: DimensionFieldKeyEnums.wealthRank,
    dataType: DimensionFieldTypeEnums.String,
    comment: '财富榜单',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: ['140002'],
    options: CompWealthRank,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 11,
  },
  {
    fieldName: '上市板块',
    fieldKey: DimensionFieldKeyEnums.listedIndustry,
    dataType: DimensionFieldTypeEnums.String,
    comment: '上市板块',
    defaultCompareType: DimensionFieldCompareTypeEnums.ExceptAny,
    defaultValue: ['S_10'],
    options: ListingIndustry,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 12,
  },
  {
    fieldName: '分支机构',
    fieldKey: DimensionFieldKeyEnums.companyIsBranch,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '分支机构 1:是分支机构，0:非分支机构, -1:不限',
    defaultCompareType: DimensionFieldCompareTypeEnums.ExceptAny,
    defaultValue: [1],
    options: [
      { label: '是分支机构', value: 1 },
      { label: '非分支机构', value: 0 },
      { label: '不限', value: -1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Radio,
    status: DataStatusEnums.Enabled,
    fieldOrder: 13,
  },
  {
    fieldName: '实缴比例',
    fieldKey: DimensionFieldKeyEnums.registrationRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '实缴资本与注册资本之比在80%-100%',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [[80, 100]],
    options: registrationRatioType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 14,
  },
  {
    fieldName: '纳税人资质变更',
    fieldKey: DimensionFieldKeyEnums.TaxpayerCertificationChange,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '纳税人资质变更',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [],
    options: [{}],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 15,
  },
  {
    fieldName: '国标行业',
    fieldKey: DimensionFieldKeyEnums.companyIndustry,
    dataType: DimensionFieldTypeEnums.String,
    comment: '国标行业类型 K-70-701(房地产开发经营);',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['K-70-701'],
    options: [{ value: 'K-70-701', label: '房地产开发经营' }],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 16,
  },
  {
    fieldName: '企查查行业',
    fieldKey: DimensionFieldKeyEnums.qccIndustry,
    dataType: DimensionFieldTypeEnums.String,
    comment: '企查查行业类型 28-2801(房地产开发)',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['28-2801'],
    options: [{ value: '28-2801', label: '房地产开发' }],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 17,
  },
];
