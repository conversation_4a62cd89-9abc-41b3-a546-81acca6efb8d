import { flattenDeep, pick } from 'lodash';
import { DimensionHitResultPO } from '../../model/diligence/dimension/DimensionHitResultPO';
import { DimensionHitStrategyPO } from '../../model/diligence/dimension/DimensionHitStrategyPO';
import { MetricScorePO } from '../../model/metric/MetricScorePO';
import { StrategyRoleEnums } from '../../enums/StrategyRoleEnums';
import { RiskModelTypeEnums } from '../../enums/RiskModelTypeEnums';

/** 过滤掉命中维度中，近过滤项的维度 */
export const exceptOnlyFilterDimension = (dimHitRes: DimensionHitResultPO[]): DimensionHitResultPO[] => {
  return dimHitRes.filter((hitRes) => hitRes.strategyRole != StrategyRoleEnums.OnlyFilter);
};

/**
 * 获取所有命中指标的维度维度命中详情 DimensionHitResultPO
 * @param originalHits
 * @returns
 */
export const flattenDimensionHitResultPO = (originalHits: MetricScorePO[]): DimensionHitResultPO[] => {
  // @ts-ignore
  return flattenDeep(
    originalHits.map((oh) => {
      return [oh.hitDetails.must, oh.hitDetails.should, oh.hitDetails.must_not];
    }),
  ).filter((t) => t);
};

/**
 * 获取所有命中指标的维度维度命中详情 DimensionHitResultPO
 *
 * 如果是尽调模型，则只关心 MetricScorePO.hitDetails 主命中策略
 *
 * 如果是监控模型，MetricScorePO.hitDetails 主命中策略 和 MetricScorePO.otherHitDetails 其他命中策略
 *
 * @param originalHits
 */
export const flattenDimensionHitResultPOV2 = (originalHits: MetricScorePO[], modelType?: RiskModelTypeEnums): DimensionHitResultPO[] => {
  // @ts-ignore
  return flattenDeep(
    originalHits.map((oh) => {
      const a = [oh.hitDetails.must, oh.hitDetails.should, oh.hitDetails.must_not];
      if (modelType == RiskModelTypeEnums.MonitorModel) {
        // 监控模型，还需要加上 MetricScorePO.otherHitDetails 其他命中策略
        oh.otherHitDetails.forEach((ohd) => {
          a.push(ohd.must, ohd.should, ohd.must_not);
        });
      }
      return a;
    }),
  ).filter((t) => t);
};

/**
 * 生成维度命中说明
 * @param template
 * @param data
 * @returns
 */
export const getDimensionDescription = (template: string, data: any) => {
  return template?.replace(/#\w*#/g, (m) => {
    const key = m.replace(/#/g, '');
    let value = data[key];
    if (key != 'count' && data[key] && data[key] >= 1000) {
      // 使用 Intl.NumberFormat 格式化数字
      const formatter = new Intl.NumberFormat('en-US', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
      value = formatter.format(data[key]);
    }
    switch (key) {
      case 'ratio': {
        return value ? `${value}%` : '- ';
      }
      case 'cycle': {
        return value > 0 ? `近${value}年` : '';
      }
      case 'amountW': {
        return value ? `${value}万元` : ' - ';
      }
      case 'amountY':
      case 'amount2Y': {
        return value ? `${value}亿元` : ' - ';
      }
      case 'amountMonth': {
        return value ? `${value}个月` : ' - ';
      }
      case 'isHidden': {
        return data['amountW'] ? '' : 'isHidden';
      }
      case 'isHiddenY': {
        return data['amountY'] ? '' : 'isHidden';
      }
      case 'percent': {
        if (value === 0) {
          return '0';
        }
        return value ? value.toString() : ' - ';
      }
      default:
        return value ? value.toString() : ' - ';
    }
  });
};

/**
 * 构造维度策略命中返回对象
 * @param dimStrategy
 * @param totalHits
 * @param desData
 * @param noTemplate  true 表示不使用template 直接展示desData
 * @returns
 */
export const processDimHitResPO = (dimStrategy: DimensionHitStrategyPO, totalHits: number, desData?: any, noTemplate = false): DimensionHitResultPO => {
  const dimHitResPO = Object.assign(
    new DimensionHitResultPO(),
    pick(dimStrategy, ['strategyId', 'strategyName', 'strategyRole', 'status', 'dimensionFilter']),
    {
      // strategyId: dimStrategy.strategyId,
      // strategyName: dimStrategy.strategyName,
      // strategyRole: dimStrategy.strategyRole,
      // status: dimStrategy.status,
      dimensionKey: dimStrategy.dimensionDef.key,
      dimensionName: dimStrategy.dimensionDef.name,
      source: dimStrategy.dimensionDef.source,
      totalHits,
      // dimensionFilter: dimStrategy,
    },
  );

  if (noTemplate) {
    dimHitResPO.description = desData;
  } else if (dimStrategy.template && desData) {
    desData['name'] = dimStrategy.strategyName;
    dimHitResPO.description = getDimensionDescription(dimStrategy.template, Object.assign(desData, { count: totalHits }));
  }
  return dimHitResPO;
};

/**
 * 构造维度策略未命中返回对象
 * @param def
 * @returns
 */
export const processNoHitDimResPO = (d: DimensionHitStrategyPO) => {
  const dimHitResPO = Object.assign(new DimensionHitResultPO(), {
    strategyId: d.strategyId,
    strategyName: d.strategyName,
    status: d.status,
    dimensionKey: d.dimensionDef.key,
    dimensionName: d.dimensionDef.name,
    source: d.dimensionDef.source,
    totalHits: 0,
  });

  return dimHitResPO;
};
