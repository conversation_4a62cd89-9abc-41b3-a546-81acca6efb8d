import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export enum MetricDynamicDataSourceTypeEnum {
  Persistent = 0, //常规持久化的数据, 如 股权冻结，行政处罚维度等
  Dynamic = 1, //基于动态表的数据
}

export enum DiligenceTargetTypeEnum {
  All = 0, //不限
  PrimaryCompnay = 1, // 目标主体
  RelatedCompany = 2, // 关联方
}

export class MetricDynamicStrategy {
  // 如果是使用了多个数据源的复合，那么就用 Persistent, 默认是都是从动态表中来获取数据
  @ApiPropertyOptional({ description: '判断指标是否命中的时候使用的主要数据源是什么, 0: 常规持久化的数据, 1: 动态表的数据' })
  @IsOptional()
  mainDataSourceType?: MetricDynamicDataSourceTypeEnum = MetricDynamicDataSourceTypeEnum.Dynamic;

  @ApiPropertyOptional({ description: '是否允许生成多条动态' })
  @IsOptional()
  allowMultipleDynamics?: boolean = false;

  @ApiPropertyOptional({ description: '是否允许同样的生成多条动态' })
  @IsOptional()
  allowRepeatedHits?: boolean = false;

  @ApiPropertyOptional({ description: '判断重复动态时是否忽略-1动态, 例如： 【实控人控制企业集中注册且实缴异常】只有存在非-1的动态才认为需要进行重复动态验证' })
  @IsOptional()
  ignorePlaceholder?: boolean = false;

  @ApiPropertyOptional({ description: '动态的生命周期，单位为天(多少天之内的命中算重复命中)' })
  //假设是7，表示7天之内如果有一样的指标命中，则算重复命中，如果7天之内没有命中，那就不算重复命中
  // 可以配合 allowRepeatedHits=false 来实现， 指定天数内命中过就不提醒，但是指定天数内没有命中过就重新提醒
  lifeCycle?: number = 0;

  @ApiPropertyOptional({
    description:
      '当allowRepeatedHits=false时，判断两次命中是否是相同，0: 通过查询相同的companyMetricsHashkey动态是否存在判断(默认逻辑) 1: 通过对比两次尽调命中结果数据内容判断是否相同 ',
  })
  isSameMetricStrategy?: number = 0;

  @ApiPropertyOptional({ description: '指标针排查/监控对象：0-不限, 1-目标主体, 2-目标主体关联方 ' })
  target?: DiligenceTargetTypeEnum = DiligenceTargetTypeEnum.All;

  @ApiPropertyOptional({ description: '关联方动态穿透：0-不穿透，1-关联方动态穿透到主体，动态复制一份给主体' })
  dynamicTransmit?: number = 0;
}
