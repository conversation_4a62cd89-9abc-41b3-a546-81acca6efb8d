import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ApiResponseStatusEnum } from '../../../../enums/ApiResponseStatusEnum';
import { DimensionSourceEnums } from '../../../../enums/diligence/DimensionSourceEnums';
import { RoverExceptions } from '../../../../exceptions/exceptionConstants';

export class PagingResponse {
  // PageSize: number;
  // PageIndex: number;
  // TotalRecords: number;
  @ApiProperty({ description: '每页多少条' })
  PageSize: number;
  @ApiProperty({ description: '当前多少页' })
  PageIndex: number;
  @ApiProperty({ description: '总共多少条数据' })
  TotalRecords: number;
}

export class HitDetailsBaseResponse {
  @ApiProperty({ description: '结果列表', type: PagingResponse })
  Paging: PagingResponse;
  @ApiProperty({ description: '结果列表' })
  GroupItems: any[];
  @ApiProperty({ description: '结果列表' })
  Result: any[];

  @ApiProperty({ description: '接口状态, OK 或者 FAILED' })
  status: ApiResponseStatusEnum = ApiResponseStatusEnum.OK;

  @ApiProperty({ description: '描述信息' })
  message?: string = 'success';

  @ApiProperty({ description: '发生错误时候的 errorCode' })
  errorCode?: number;

  @ApiProperty({ description: '是否实时获取详情数据， true: 实时获取， false: 快照获取，默认是false' })
  realtime?: boolean;

  @ApiPropertyOptional({ description: '快照状态' })
  snapshotStatus?: ApiResponseStatusEnum;

  @ApiPropertyOptional({ description: '前端详情展示规则key' })
  displayKey?: string;

  constructor() {
    this.Paging = {
      TotalRecords: 0,
      PageIndex: 1,
      PageSize: 5,
    };
    this.GroupItems = [];
    this.Result = [];
    this.status = ApiResponseStatusEnum.OK;
    this.message = 'success';
  }

  static ok(realtime?: boolean) {
    const b = new HitDetailsBaseResponse();
    b.realtime = realtime;
    return b;
  }

  static failed(message?: string, source?: DimensionSourceEnums, code?: number) {
    const res = new HitDetailsBaseResponse();
    res.status = ApiResponseStatusEnum.FAILED;
    res.message = message || RoverExceptions.Diligence.Detail.GetDetailsFailed.error;
    if (source) {
      res.message = `${res.message}:source=${source}`;
    }
    res.errorCode = code || RoverExceptions.Diligence.Detail.GetDetailsFailed.code;
    return res;
  }

  @ApiPropertyOptional({ description: '如果指定了聚合，这里会返回聚合结果' })
  aggs?: any;
}

export class DimensionHitDetailsDescription {
  @ApiProperty({ type: String, description: '描述' })
  description: string;
}
