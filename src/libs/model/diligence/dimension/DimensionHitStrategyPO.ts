import { ApiProperty, PickType } from '@nestjs/swagger';
import { DimensionHitStrategyEntity } from '../../../entities/DimensionHitStrategyEntity';
import { DimensionHitStrategyFieldsEntity } from '../../../entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldKeyEnums } from '../../../enums/dimension/dimension.filter.params';
import { DimensionDefinitionEntity } from '../../../entities/DimensionDefinitionEntity';
import { DimensionTypeEnums } from '../../../enums/diligence/DimensionTypeEnums';
import { DimensionFieldCompareTypeEnums } from '../../../enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFilterParams } from './DimensionStrategyPO';

export class SortFieldPO {
  @ApiProperty({ description: '排序参数' })
  field: string;
  @ApiProperty({ description: '快照排序参数' })
  fieldSnapshot?: string;
  @ApiProperty({ description: '排序规则', enum: ['ASC', 'DESC'] })
  order: 'ASC' | 'DESC';
}

/**
 * 维度命中策略
 */
export class DimensionHitStrategyPO extends PickType(DimensionHitStrategyEntity, [
  'strategyId',
  'strategyName',
  'strategyRole',
  'template',
  'status',
  'fieldHitStrategy',
  'dimensionDef',
  'strategyFields',
  'extendJson',
] as const) {
  constructor(dimensionDef: DimensionDefinitionEntity) {
    super();
    this.dimensionDef = dimensionDef;
    this.key = this.dimensionDef?.key;
    this.source = this.dimensionDef?.source;
  }

  key: DimensionTypeEnums;

  source: string;
  /**
   * 找到指fieldKey对应的命中参数设置
   * @param fieldKey
   * @returns
   */
  getStrategyFieldByKey = (fieldKey: DimensionFieldKeyEnums): DimensionHitStrategyFieldsEntity => {
    return this.strategyFields?.find((sf) => sf?.dimensionFieldKey === fieldKey);
  };

  /**
   * 获取统计周期
   * @returns
   */
  getCycle = (): number => {
    return this.strategyFields?.find((sf) => sf?.dimensionFieldKey === DimensionFieldKeyEnums.cycle)?.fieldValue[0] as number;
  };

  /**
   * 获取统计周期字段设置
   * @returns
   */
  getCycleField = (): DimensionHitStrategyFieldsEntity => {
    const field = this.strategyFields?.find((sf) => sf?.dimensionFieldKey === DimensionFieldKeyEnums.cycle);
    // 未设置 compareType 默认使用 近x年 逻辑， 发生时间 ≥ 通过cycle取到的时间；
    if (field && !field.compareType) {
      field.compareType = DimensionFieldCompareTypeEnums.GreaterThan;
    }
    return field;
  };

  getIsValid = (): number => {
    return this.strategyFields?.find((sf) => sf?.dimensionFieldKey === DimensionFieldKeyEnums.isValid)?.fieldValue[0] as number;
  };

  /**
   * 获取维度列表排序
   * @returns
   */
  getSortField = (): SortFieldPO => {
    const field = this.strategyFields?.find((sf) => sf?.dimensionFieldKey === DimensionFieldKeyEnums.sortField)?.fieldValue;
    return field?.[0] as SortFieldPO;
  };

  // //以下 临时定义
  // sort: number; //临时定义，占位用，这个应该是从 group_metric_relation 里面取
  // isVirtualDimension?: number;
  // subDimensionList?: object[];

  @ApiProperty({ description: '独立的维度过滤条件' })
  dimensionFilter?: DimensionFilterParams;
}
