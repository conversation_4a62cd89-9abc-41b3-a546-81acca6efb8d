import { Test, TestingModule } from '@nestjs/testing';
import * as Bluebird from 'bluebird';
import * as moment from 'moment';
import { EntityManager, getConnection, getRepository, In, Repository } from 'typeorm';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { MonitorCompanyRelatedDailyEntity } from '../../../../libs/entities/MonitorCompanyRelatedDailyEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorMetricsDynamicEntity } from '../../../../libs/entities/MonitorMetricsDynamicEntity';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { DimensionTypeEnums } from '../../../../libs/enums/diligence/DimensionTypeEnums';
import { NebulaRelatedEdgeEnums } from '../../../../libs/enums/dimension/NebulaRelatedEdgeEnums';
import { PlatformUser } from '../../../../libs/model/common';
import { QueryMonitorDynamicDetialsRequest } from '../../../../libs/model/monitor/QueryMonitorDynamicDetialsRequest';
import { AppTestModule } from '../../../app/app.test.module';
import { BatchInfoPO } from '../../../batch/model/BatchInfoPO';
import { EvaluationService } from '../../../diligence/evaluation/evaluation.service';
import { DiligenceSnapshotService } from '../../../diligence/snapshot/diligence.snapshot.service';
import { SnapshotQueueTypeEnums } from '../../../diligence/snapshot/po/SnapshotQueueTypeEnums';
import { ModelInitICBCSZMonitorService } from '../../../risk_model/init_mode/model.init.monitor.icbc.sz.service';
import { clearMonitorTestData } from '../../../test_utils_module/monitor.test.tools';
import { generateUniqueTestIds, getTestUser } from '../../../test_utils_module/test.user';
import { MonitorCompanyService } from '../../company/monitor.company.service';
import { MonitorGroupService } from '../../group/monitor.group.service';
import { MonitorModule } from '../../monitor.module';
import { AnalyzeMonitorDynamicMessagePO } from '../../po/AnalyzeMonitorDynamicMessagePO';
import { MonitorDynamicProcessModule } from './monitor-dynamic-process.module';
import { MonitorDynamicMessageListenerV2 } from './monitor-dynamic-message-listener-v2';
import { MonitorCompanyRelatedStatusEnum } from '../../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import { MonitorCompanyEntity } from '../../../../libs/entities/MonitorCompanyEntity';
import { GetHitDetailsParamBase } from '../../../../libs/model/diligence/req&res/GetHitDetailsParam';
import { HitDetailsBaseQueryParams } from '../../../../libs/model/diligence/details/request';
import { getDimensionRecordIdForSnapshot } from '../../../diligence/snapshot/utils.snapshot';

// 测试数据
const [testOrgId, testUserId] = generateUniqueTestIds('monitor.dynamic.message.v2.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(600000);
describe('集成测试-监控生成动态', () => {
  process.env.MOCK_MESSAGE_QUEUE_NOT = 'true';
  process.env.STAGE = 'local';
  let monitorCompanyService: MonitorCompanyService;
  let monitorGroupService: MonitorGroupService;
  let monitorDynamicServiceV2: MonitorDynamicMessageListenerV2;
  // let monitorDynamicService: MonitorDynamicMessageListener;
  let monitorDynamicRepo: Repository<MonitorMetricsDynamicEntity>;
  let entityManager: EntityManager;
  let modelInitICBCSZMonitorService: ModelInitICBCSZMonitorService;
  let evaluationService: EvaluationService;
  let snapshotService: DiligenceSnapshotService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, MonitorModule, MonitorDynamicProcessModule],
    }).compile();
    monitorCompanyService = module.get<MonitorCompanyService>(MonitorCompanyService);
    monitorGroupService = module.get<MonitorGroupService>(MonitorGroupService);
    // monitorDynamicService = module.get(MonitorDynamicMessageListener);
    monitorDynamicServiceV2 = module.get(MonitorDynamicMessageListenerV2);
    modelInitICBCSZMonitorService = module.get(ModelInitICBCSZMonitorService);
    monitorDynamicRepo = getRepository(MonitorMetricsDynamicEntity);
    entityManager = monitorDynamicRepo.manager;
    evaluationService = module.get(EvaluationService);
    snapshotService = module.get(DiligenceSnapshotService);

    // await clearMonitorTestData(entityManager, testUser);
  });

  afterAll(async () => {
    const connection = getConnection();
    await connection.close();
  });

  // 在函数外部保存原始方法，避免重复保存
  let originalFetchDimensionDetails: any = null;

  const onlyDoMonitor = async (times: number, user: PlatformUser, groupId: number, modelId: number, batchStartTime: number, batchEndTime: number) => {
    console.log(`第 ${times} 次执行尽调，时间区间为${batchStartTime} - ${batchEndTime}`);

    // 第一次调用时保存原始方法
    if (!originalFetchDimensionDetails) {
      originalFetchDimensionDetails = snapshotService.fetchDimensionDetails.bind(snapshotService);
    }

    // 清理之前的 mock，避免叠加
    jest.clearAllMocks();

    // mock 消费生成快照消息
    jest.spyOn(snapshotService.snapshotBatchQueue, 'sendMessageV2').mockImplementation((msg) => {
      return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.BatchDiligence);
    });

    // 针对纳税人资质变化指标, mock快照数据，第一次为小规模纳税人，第二次以后为一般纳税人
    jest
      .spyOn(snapshotService, 'fetchDimensionDetails')
      .mockImplementation(async (hitDetailParam: GetHitDetailsParamBase, params: HitDetailsBaseQueryParams, snapshotId: string, fullSnapshot?: boolean) => {
        if (hitDetailParam.key === DimensionTypeEnums.TaxpayerCertificationChange) {
          const description = times == 1 ? '小规模纳税人' : '一般纳税人';
          const recordId = getDimensionRecordIdForSnapshot(hitDetailParam.key, { description }, params.keyNo, user.currentOrg);
          return Promise.resolve([{ description, recordId }]);
        } else {
          // 调用保存的原始方法
          return originalFetchDimensionDetails(hitDetailParam, params, snapshotId, fullSnapshot);
        }
      });

    // 第一次batch查询时间取消
    const batchInfo: BatchInfoPO = {
      asyncBatch: 1, // 标记为异步启动批次，需要后续手动启动
      orgModelIds: [modelId],
      cacheHours: 0,
      currentUser: user,
      monitorGroupId: groupId,
      batchStartTime, // 设置动态时间区间范围
      batchEndTime,
    };

    const batch = Object.assign(new BatchEntity(), {
      product: user.currentProduct,
      createDate: new Date(),
      orgId: user.currentOrg,
      createBy: user.userId,
      businessType: BatchBusinessTypeEnums.Diligence_Continuous,
      batchInfo: batchInfo,
      recordCount: 0,
      statisticsInfo: {
        totalCount: 0,
        placeholderCount: 0,
        nonRepeatableCount: 0,
      },
    });

    const batchEntity = await entityManager.save(batch);

    const { data: monitorCompanies } = await monitorCompanyService.searchMonitorCompany(
      {
        groupIds: [groupId],
        pageSize: 20,
      },
      user,
    );

    expect(monitorCompanies.length).toBeGreaterThan(0);

    // 第一次对监控分组内的企业执行尽调

    const relateds = await entityManager.find(MonitorCompanyRelatedPartyEntity, {
      where: { monitorGroupId: groupId, companyIdRelated: In(monitorCompanies.map((i) => i.companyId)) },
    });

    const diligenceList = await Bluebird.map(monitorCompanies, async (company) => {
      let isRelated = company.primaryObject === 0;
      if (!isRelated && relateds?.find((r) => r.companyIdRelated === company.companyId)) {
        isRelated = true;
      }

      const diligence = await evaluationService.runMonitorRisk(
        user.currentOrg,
        {
          companyId: company.companyId,
          companyName: company.companyName,
          orgModelIds: [modelId],
          batchId: batchEntity.batchId,
          isRelated,
        },
        'SAAS_PRO',
      );
      return diligence;
    });

    const dynamicMsgPO: AnalyzeMonitorDynamicMessagePO = {
      orgId: user.currentOrg,
      product: user.currentProduct,
      monitorGroupId: groupId,
      diligenceIds: diligenceList.map((f) => f.id),
      batchId: batchEntity.batchId,
      retryCount: 0,
    };

    return dynamicMsgPO;
  };

  it('sf模型-多次执行监控任务，验证动态生成逻辑', async () => {
    await clearMonitorTestData(entityManager, testUser);

    // 重新创建监控模型
    const modelEntity = await modelInitICBCSZMonitorService.createMonitorModel(testUser, '自动创建icbcsf监控模型001', testUser.currentOrg);
    try {
      // 初始话创建默认监控分组
      const { monitorGroupId } = await monitorGroupService.initMonitor(testUser);

      const companyId = 'acfb204d6fbe49198a12c8279e7b2d20';
      const companyName = '永赢金融租赁有限公司';
      const relateds = [
        {
          companyId: '4e3587df894bcdea475124d0eeacb230',
          companyName: '上海永赢沪畅翔一号飞机租赁有限公司',
          relatedType: NebulaRelatedEdgeEnums.ActualController,
        },
        {
          companyId: '8ca43ae432dd1ecc64f9f47b2325833e',
          companyName: '上海永赢沪畅翔二号飞机租赁有限公司',
          relatedType: NebulaRelatedEdgeEnums.ActualController,
        },
        { companyId: 'f625a5b661058ba5082ca508f99ffe1b', companyName: '企查查科技股份有限公司', relatedType: NebulaRelatedEdgeEnums.ActualController },
      ];

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId,
        items: [
          { companyId, companyName },
          { companyId: 'eb195eecaf0c5afdee462e6d27e74106', companyName: '山东省互联网传媒集团股份有限公司' },
        ],
      });

      // 给 永赢金融租赁有限公司 4e3587df894bcdea475124d0eeacb230  添加关联方
      await monitorCompanyService.addRelatedCompany(testUser, {
        monitorGroupId,
        companyId,
        items: relateds,
      });

      // 执行第一次监控任务
      const dynamicMsgPO1 = await onlyDoMonitor(
        1,
        testUser,
        monitorGroupId,
        modelEntity.modelId,
        // 监控开始时间
        moment().subtract(3, 'month').unix(),
        // 监控结束时间
        moment().subtract(2, 'month').unix(),
      );

      // 第一次批量尽调生成占位动态
      const batchInsertDynamic1 = await monitorDynamicServiceV2.handleMetricsAnalyze(dynamicMsgPO1);
      expect(batchInsertDynamic1.totalCount).toBeGreaterThan(0);
      // 第一次批量尽调的动态全是占位动态
      expect(batchInsertDynamic1.placeholderCount).toEqual(batchInsertDynamic1.totalCount);

      // 给companyId 添加关联方日志, 用于第二次执行监控任务处罚关联方变化动态
      await entityManager.save(MonitorCompanyRelatedDailyEntity, {
        orgId: testUser.currentOrg,
        companyId,
        monitorGroupId,
        relatedIds: relateds.map((item) => item.companyId).join(','),
        updateTime: new Date(),
        product: testUser.currentProduct,
      });

      // 执行第2次监控任务
      const dynamicMsgPO2 = await onlyDoMonitor(
        2,
        testUser,
        monitorGroupId,
        modelEntity.modelId,
        // 监控开始时间
        moment().subtract(2, 'month').unix(),
        // 监控结束时间
        moment().subtract(1, 'month').unix(),
      );

      // 第2次批量尽调后生成动态
      const batchInsertDynamic2 = await monitorDynamicServiceV2.handleMetricsAnalyze(dynamicMsgPO2);
      expect(batchInsertDynamic2.totalCount).toBeGreaterThan(0);

      // 第2次批量尽调后生成动态 没有占位动态
      expect(batchInsertDynamic2.placeholderCount).toBe(0);

      // 查找关联方变更的动态
      const dynamicRelated = await entityManager.findOne(MonitorMetricsDynamicEntity, {
        monitorGroupId,
        batchId: dynamicMsgPO2.batchId,
        orgId: testOrgId,
        metricsName: '关联方',
      });

      expect(dynamicRelated.metricsContent).toBeDefined();

      const strategies = dynamicRelated.metricsContent.metricScorePO.hitDetails.should;
      const strategyIdadd = strategies.find((s) => s.strategyName == '关联方新增');

      // 查找关联方新增
      expect(strategyIdadd).toBeDefined();
      const param: QueryMonitorDynamicDetialsRequest = {
        orgId: testOrgId,
        companyId: dynamicRelated.companyId,
        dimensionKey: DimensionTypeEnums.RelatedCompanyChange,
        strategyId: strategyIdadd.strategyId,
        diligenceId: dynamicRelated.diligenceId,
        batchId: dynamicRelated.batchId,
        preBatchId: dynamicMsgPO1.batchId,
        monitorGroupId,
        pageIndex: 1,
        pageSize: 20,
      };
      const result = await monitorDynamicServiceV2.fatchDimensionHitDetails(param);
      expect(result.data.length).toBeLessThanOrEqual(result.total);

      // 查找关联方减少
      const strategyIdremove = strategies.find((s) => s.strategyName == '关联方失效');
      expect(strategyIdremove).toBeDefined();
      const param2: QueryMonitorDynamicDetialsRequest = {
        orgId: testOrgId,
        companyId: dynamicRelated.companyId,
        dimensionKey: DimensionTypeEnums.RelatedCompanyChange,
        strategyId: strategyIdremove.strategyId,
        diligenceId: dynamicRelated.diligenceId,
        batchId: dynamicRelated.batchId,
        preBatchId: dynamicMsgPO1.batchId,
        monitorGroupId,
        pageIndex: 1,
        pageSize: 20,
      };

      const resultRemove = await monitorDynamicServiceV2.fatchDimensionHitDetails(param2);

      expect(resultRemove.data[0].companyKeynoRelated).toEqual('f625a5b661058ba5082ca508f99ffe1b');

      // 关联方失效验证
      const relatedCompanyEntity = await entityManager.findOne(MonitorCompanyRelatedPartyEntity, {
        companyIdRelated: resultRemove.data[0].companyKeynoRelated,
        companyIdPrimary: companyId,
        monitorGroupId,
      });

      expect(relatedCompanyEntity.status).toEqual(MonitorCompanyRelatedStatusEnum.Invalid);

      // 主体的关联方变更标签，hashkey验证
      expect(dynamicRelated.uniqueHashkey).toBeDefined();
      const primaryCompany = await entityManager.findOne(MonitorCompanyEntity, { monitorGroupId, companyId });

      expect(dynamicRelated.uniqueHashkey).toBe(primaryCompany.relatedDynamicHashKey);

      // 执行第3次监控任务
      const dynamicMsgPO3 = await onlyDoMonitor(
        3,
        testUser,
        monitorGroupId,
        modelEntity.modelId,
        // 监控开始时间
        moment().subtract(1, 'month').unix(),
        // 监控结束时间
        moment().subtract(1, 'day').unix(),
      );

      // 第3次批量尽调后生成动态
      const batchInsertDynamic3 = await monitorDynamicServiceV2.handleMetricsAnalyze(dynamicMsgPO3);
      expect(batchInsertDynamic3.totalCount).toBeGreaterThan(0);

      // 第3次批量尽调后生成动态 没有占位动态
      expect(batchInsertDynamic3.placeholderCount).toBe(0);

      //查找 第一次 实控人控制企业集中注册且实缴异常的动态
      const dynamicControllerCompany1 = await entityManager.findOne(MonitorMetricsDynamicEntity, {
        monitorGroupId,
        batchId: dynamicMsgPO1.batchId,
        orgId: testOrgId,
        metricsName: '实控人控制企业集中注册且实缴异常',
      });
      // 有占位动态
      expect(dynamicControllerCompany1.status).toBe(-1);
      expect(dynamicControllerCompany1.metricsContent.displayContent.length).toBe(0);

      //查找 第二次 实控人控制企业集中注册且实缴异常的动态
      const dynamicControllerCompany2 = await entityManager.findOne(MonitorMetricsDynamicEntity, {
        monitorGroupId,
        batchId: dynamicMsgPO2.batchId,
        orgId: testOrgId,
        metricsName: '实控人控制企业集中注册且实缴异常',
      });
      // 有命中
      expect(dynamicControllerCompany2.status).toBe(0);
      expect(dynamicControllerCompany2.metricsContent.displayContent.length).toBeGreaterThan(0);

      //查找 第三次 实控人控制企业集中注册且实缴异常的动态 无命中
      const dynamicControllerCompany3 = await entityManager.findOne(MonitorMetricsDynamicEntity, {
        monitorGroupId,
        batchId: dynamicMsgPO3.batchId,
        orgId: testOrgId,
        metricsName: '实控人控制企业集中注册且实缴异常',
      });
      // 无命中
      expect(dynamicControllerCompany3).toBeUndefined();

      //查找 第一次 纳税人资质状态变化 有占位动态
      const dynamicNSR1 = await entityManager.find(MonitorMetricsDynamicEntity, {
        monitorGroupId,
        batchId: dynamicMsgPO1.batchId,
        orgId: testOrgId,
        metricsName: '纳税人资质状态变化',
      });
      expect(dynamicNSR1[0].status).toBe(-1);
      expect(dynamicNSR1.length).toBeGreaterThan(0);

      //查找 第二次 纳税人资质状态变化 有命中
      const dynamicNSR2 = await entityManager.find(MonitorMetricsDynamicEntity, {
        monitorGroupId,
        batchId: dynamicMsgPO2.batchId,
        orgId: testOrgId,
        metricsName: '纳税人资质状态变化',
      });
      expect(dynamicNSR2[0].status).toBe(0);
      expect(dynamicNSR2.length).toBeGreaterThan(0);

      //查找 第三次 纳税人资质状态变化 无命中
      const dynamicNSR3 = await entityManager.find(MonitorMetricsDynamicEntity, {
        monitorGroupId,
        batchId: dynamicMsgPO3.batchId,
        orgId: testOrgId,
        metricsName: '纳税人资质状态变化',
      });
      expect(dynamicNSR3.length).toBe(0);
    } finally {
      // 清理 testUser 监控数据
      await clearMonitorTestData(entityManager, testUser);
    }
  });
});
