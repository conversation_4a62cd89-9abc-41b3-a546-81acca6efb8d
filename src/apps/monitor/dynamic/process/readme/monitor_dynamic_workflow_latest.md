# 监控动态生成处理流程 - 最新版本

## 概述

本文档基于当前代码实现，详细描述了监控动态生成的完整数据处理流程。系统采用领域服务分离和策略模式架构，实现了高度模块化和可扩展性。

## 1. 整体架构

### 1.1 核心组件

```
┌─────────────────────────────────────────────────────────────────┐
│                    MonitorDynamicMessageListenerV2              │
│                        (流程协调器)                              │
└─────────────────────┬───────────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ Diligence   │ │ Generation  │ │ Comparison  │
│ Service     │ │ Service     │ │ Service     │
└─────────────┘ └─────────────┘ └─────────────┘
        │             │             │
        └─────────────┼─────────────┘
                      │
                      ▼
              ┌─────────────┐
              │ Persistence │
              │ Service     │
              └─────────────┘
```

### 1.2 服务职责

- **DiligenceService**: 尽调记录处理和分类
- **GenerationService**: 动态生成和分类
- **ComparisonService**: 动态内容比对（使用策略模式）
- **PersistenceService**: 动态持久化和状态更新
- **RelatedService**: 关联方变更专用处理

## 2. 主流程详解

### 2.1 入口方法

```typescript
public async handleMetricsAnalyze(msgPO: AnalyzeMonitorDynamicMessagePO)
```

**输入参数**:

- `orgId`: 组织 ID
- `product`: 产品代码
- `monitorGroupId`: 监控分组 ID
- `diligenceIds`: 尽调 ID 列表
- `batchId`: 批次 ID
- `retryCount`: 重试次数

**返回结果**:

```typescript
{
  placeholderCount: number; // 占位动态数量
  repeatableCount: number; // 可重复动态数量
  nonRepeatableCount: number; // 不可重复动态数量
  totalCount: number; // 总动态数量
}
```

### 2.2 处理步骤

#### 步骤 1: 尽调记录处理

```typescript
// 1. 查找尽调记录
const diligenceEntities = await this.diligenceService.findDiligence(diligenceIds);

// 2. 分类尽调记录
const { finshedDiligence, unFinshedDiligence } = this.diligenceService.classifyDiligence(diligenceEntities, batchId);

// 3. 处理未完成尽调
await this.diligenceService.retryUnfinishedDiligence(unFinshedDiligence, msgPO, retryCount);
```

#### 步骤 2: 数据准备

```typescript
// 4. 获取基础数据
const [initDynamics, primaryCompanies] = await Promise.all([
  this.findInitDynamics(orgId, monitorGroupId, companyIds, riskModelId),
  this.findRelatedParties(monitorGroupId, companyIds),
]);
```

#### 步骤 3: 动态生成与分类

```typescript
// 5. 生成占位动态
const placeholderDynamics = this.generationService.generatePlaceholderDynamics(...);

// 6. 分类指标动态
const { repeatableDynamics, nonRepeatableDynamics } =
  await this.generationService.classifyMetricDynamics(...);
```

#### 步骤 4: 动态处理与保存

```typescript
// 7. 重置关联方变更状态
await this.relatedService.setRelatedDynamicHashKey(companyIds, monitorGroupId, '');

// 8-10. 分别保存三种类型的动态
placeholderCount = await this.persistenceService.saveDynamic(placeholderDynamics);
repeatableCount = await this.persistenceService.saveDynamic(repeatableDynamics);

// 处理不可重复动态（核心逻辑）
const processedDynamics = await this.comparisonService.processNonRepeatableDynamics(nonRepeatableDynamics);
nonRepeatableCount = await this.persistenceService.saveDynamic(processedDynamics);

// 11. 更新监控企业状态
await this.persistenceService.updateMonitorCompanyStatus(finshedDiligence, monitorGroupId, batchId, inserted);
```

## 3. 核心服务详解

### 3.1 DiligenceService (尽调处理服务)

**主要功能**:

- 查找尽调记录
- 按快照状态分类尽调记录
- 处理未完成尽调的重试逻辑

**关键方法**:

```typescript
findDiligence(diligenceIds: number[]): Promise<DiligenceHistoryEntity[]>
classifyDiligence(entities: DiligenceHistoryEntity[], batchId: number)
retryUnfinishedDiligence(unFinished: DiligenceHistoryEntity[], msgPO: any, retryCount: number)
```

### 3.2 GenerationService (动态生成服务)

**主要功能**:

- 生成占位动态
- 分类指标动态（可重复/不可重复）
- 填充动态内容（特别是关联方变更指标）

**关键方法**:

```typescript
generatePlaceholderDynamics(...): MonitorMetricDynamicEsDoc[]
classifyMetricDynamics(...): Promise<{repeatableDynamics, nonRepeatableDynamics}>
fulfillDynamicContent(dynamicList: MonitorMetricDynamicEsDoc[]): Promise<MonitorMetricDynamicEsDoc[]>
```

### 3.3 ComparisonService (动态比对服务)

**主要功能**:

- 处理不可重复动态的比对逻辑
- 使用策略模式处理不同类型指标
- 支持生命周期(lifeCycle)限制

**关键方法**:

```typescript
processNonRepeatableDynamics(dynamics: MonitorMetricDynamicEsDoc[]): Promise<MonitorMetricDynamicEsDoc[]>
compareDynamicContents(dynamic, dimHitRes, preBatchId): Promise<[number, DynamicDisplayContent[]]>
findMetricDimensions(metricScore): DimensionHitResultPO[]
```

### 3.4 PersistenceService (持久化服务)

**主要功能**:

- 保存动态到数据库和 ES
- 更新监控企业状态
- 批量处理优化

**关键方法**:

```typescript
saveDynamic(dynamics: MonitorMetricDynamicEsDoc[]): Promise<number>
updateMonitorCompanyStatus(diligences, monitorGroupId, batchId, inserted): Promise<void>
```

## 4. 策略模式架构

### 4.1 策略接口

```typescript
interface IMetricStrategy {
  canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean;
  processMetricIsRepeated(dynamic, dimHitRes, batchId, preBatchId): Promise<[boolean, MonitorMetricDynamicEsDoc]>;
  compareContent(dynamic, dimHitRes, preBatchId): Promise<[number, DynamicDisplayContent[]]>;
}
```

### 4.2 策略实现

#### RegularMetricStrategy (常规指标策略)

- 处理大多数普通指标
- 通过快照比对检测内容变化
- 支持新增数据的检测

#### TaxpayerMetricStrategy (纳税人资质变更策略)

- 专门处理纳税人资质变更指标
- 特殊的比对逻辑，比较纳税人资质变更前后的数据差异，增加或者减少

#### 策略选择逻辑

```typescript
public getStrategy(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): IMetricStrategy {
  if (this.taxpayerMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
    return this.taxpayerMetricStrategy;
  }
  // 默认使用常规策略
  return this.regularMetricStrategy;
}
```

## 5. 关联方变更指标处理

### 5.1 处理流程

关联方变更指标的处理在 `GenerationService.fulfillDynamicContent` 方法中：

```typescript
// 1. 识别关联方变更指标
if (this.relatedService.isRelatedMetric(dimHitRes)) {
  // 2. 设置关联方变更动态标记
  await this.relatedService.setRelatedDynamicHashKey([companyId], monitorGroupId, uniqueHashkey);

  // 3. 处理每个维度命中结果
  await Bluebird.map(dimHitRes, async (dimHit) => {
    // 4. 获取关联方变更详情
    const searchRes = await this.detailsService.fatchDimensionHitDetails({...});

    // 5. 根据策略名称判断操作类型
    if (strategyName == '关联方新增') {
      // 6a. 处理关联方新增
      await this.relatedService.updateRelated(searchRes.data, monitorGroupId, companyId, Invalid);
    } else {
      // 6b. 处理关联方失效
      await this.relatedService.updateRelated(searchRes.data, monitorGroupId, companyId, Valid);
    }

    // 7. 添加动态内容
    dynamic.metricsContent.displayContent.push(dynamicAdd);
  });
}
```

### 5.2 RelatedService 核心方法

```typescript
// 判断是否为关联方变更指标
isRelatedMetric(dimHitRes: DimensionHitResultPO[]): boolean

// 设置关联方变更动态标记
setRelatedDynamicHashKey(companyIds: string[], monitorGroupId: number, hashKey: string): Promise<void>

// 更新关联方状态
updateRelated(relatedChangeCompany, monitorGroupId, companyIdPrimary, relatedStatus): Promise<void>
```

## 6. 不可重复动态处理逻辑

### 6.1 处理流程

```typescript
public async processNonRepeatableDynamics(nonRepeatableDynamics: MonitorMetricDynamicEsDoc[]) {
  const processedDynamics: MonitorMetricDynamicEsDoc[] = [];

  await Bluebird.map(nonRepeatableDynamics, async (dynamic) => {
    const { isSameMetricStrategy, lifeCycle } = dynamic.metricsContent.metricScorePO?.detailsJson?.dynamicStrategy;

    // 1. 查询历史动态记录
    const preMetricDynamic = await this.findPreviousDynamic(dynamic, lifeCycle);

    if (!preMetricDynamic) {
      // 2. 没有历史记录，直接添加
      processedDynamics.push(dynamic);
    } else if (isSameMetricStrategy == 1) {
      // 3. 需要内容比对
      const strategy = this.metricStrategyFactory.getStrategy(metricScorePO, dimHitRes);
      const [isRepeated, processedDynamic] = await strategy.processMetricIsRepeated(dynamic, dimHitRes, batchId, preBatchId);

      if (!isRepeated) {
        processedDynamics.push(processedDynamic);
      }
    }
  });

  return processedDynamics;
}
```

### 6.2 生命周期支持

系统支持动态的生命周期限制：

- `lifeCycle`: 动态生命周期天数
- 在指定天数内，相同指标不会重复生成动态
- 查询条件：`dynamic.createDate > startDate`

### 6.3 内容比对策略

- `isSameMetricStrategy = 0`: 只要指标命中就生成动态
- `isSameMetricStrategy = 1`: 需要比对具体内容，有变化才生成动态

## 7. 逻辑漏洞分析

### 7.1 发现的问题

#### 问题 1: 关联方变更指标处理的性能问题

**位置**: `GenerationService.fulfillDynamicContent`
**问题**:

- 对每个维度命中结果都调用 `fatchDimensionHitDetails`
- 每次都调用 `updateRelated` 更新数据库
- 缺乏批量处理优化

#### 问题 2: 策略工厂中关联方策略被注释

**位置**: `MetricStrategyFactory`
**问题**:

- 关联方变更策略类被注释掉
- 关联方变更逻辑散落在 `GenerationService` 中
- 违反了策略模式的设计原则

#### 问题 3: 错误处理不完善

**位置**: 多个服务
**问题**:

- 缺乏对外部服务调用失败的处理
- 数据库操作异常处理不完善
- 缺乏事务管理

#### 问题 4: 并发控制不一致

**位置**: 多个服务
**问题**:

- 不同地方使用不同的并发数量
- 缺乏统一的并发控制策略

### 7.2 数据一致性风险

#### 风险 1: 关联方状态更新竞态条件

- 多个动态同时更新同一关联方状态
- 可能导致状态不一致

#### 风险 2: 动态生成与 ES 索引不同步

- 数据库插入成功但 ES 索引失败
- 可能导致查询结果不一致

## 8. 优化建议

### 8.1 关联方变更指标优化

#### 优化方案 1: 批量处理

```typescript
// 当前实现 - 逐个处理
await Bluebird.map(dimHitRes, async (dimHit) => {
  const searchRes = await this.detailsService.fatchDimensionHitDetails({...});
  await this.relatedService.updateRelated(searchRes.data, ...);
});

// 优化后 - 批量处理
const allRelatedChanges = await this.batchFetchRelatedChanges(dimHitRes, dynamic);
await this.relatedService.batchUpdateRelated(allRelatedChanges, monitorGroupId, companyId);
```

#### 优化方案 2: 恢复策略模式

```typescript
// 创建关联方变更策略
@Injectable()
export class RelatedCompanyMetricStrategy extends BaseMetricStrategy {
  canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean {
    return dimHitRes[0].dimensionKey === DimensionTypeEnums.RelatedCompanyChange;
  }

  async compareContent(dynamic, dimHitRes, preBatchId): Promise<[number, DynamicDisplayContent[]]> {
    // 专门的关联方变更比对逻辑
  }
}
```

#### 优化方案 3: 缓存优化

```typescript
// 添加关联方状态缓存
@Injectable()
export class RelatedCompanyCacheService {
  private cache = new Map<string, MonitorCompanyRelatedStatusEnum>();

  async getRelatedStatus(monitorGroupId: number, companyId: string): Promise<MonitorCompanyRelatedStatusEnum> {
    const key = `${monitorGroupId}_${companyId}`;
    if (!this.cache.has(key)) {
      const status = await this.fetchFromDatabase(monitorGroupId, companyId);
      this.cache.set(key, status);
    }
    return this.cache.get(key);
  }
}
```

### 8.2 性能优化

#### 优化 1: 数据库查询优化

```typescript
// 当前实现 - 多次查询
const preMetricDynamic = await qb.orderBy('dynamic.createDate', 'DESC').getOne();

// 优化后 - 批量查询
const batchQuery = this.dynamicRepo.createQueryBuilder().where('companyMetricsHashkey IN (:...hashkeys)', { hashkeys }).andWhere('status > -1');
const existingDynamics = await batchQuery.getMany();
```

#### 优化 2: 并发控制统一

```typescript
// 配置化并发控制
export const CONCURRENCY_CONFIG = {
  DYNAMIC_PROCESSING: 5,
  RELATED_UPDATES: 3,
  CONTENT_FILLING: 10,
};

// 使用统一配置
await Bluebird.map(dynamics, processor, {
  concurrency: CONCURRENCY_CONFIG.DYNAMIC_PROCESSING,
});
```

### 8.3 错误处理优化

#### 优化 1: 添加重试机制

```typescript
async function withRetry<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, i)));
    }
  }
}
```

#### 优化 2: 事务管理

```typescript
async saveDynamic(dynamics: MonitorMetricDynamicEsDoc[]): Promise<number> {
  return this.dynamicRepo.manager.transaction(async (manager) => {
    // 数据库操作
    const result = await manager.save(dynamics);

    try {
      // ES索引操作
      await this.dynamicEsService.insertDynamicDoc(dynamics);
    } catch (esError) {
      // ES失败时记录日志，但不回滚数据库操作
      this.logger.error('ES indexing failed', esError);
    }

    return result.length;
  });
}
```

## 9. 扩展指南

### 9.1 添加新指标策略

1. 创建策略类

```typescript
@Injectable()
export class NewMetricStrategy extends BaseMetricStrategy {
  canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean {
    return dimHitRes.some((dim) => dim.dimensionKey === 'NEW_DIMENSION_TYPE');
  }

  async compareContent(dynamic, dimHitRes, preBatchId): Promise<[number, DynamicDisplayContent[]]> {
    // 实现特定比对逻辑
  }
}
```

2. 注册到工厂

```typescript
// 在 MetricStrategyFactory 中添加
constructor(
  private readonly newMetricStrategy: NewMetricStrategy,
  // ...其他策略
) {}

public getStrategy(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): IMetricStrategy {
  if (this.newMetricStrategy.canHandle(metricScorePO, dimHitRes)) {
    return this.newMetricStrategy;
  }
  // ...其他判断
}
```

### 9.2 添加新服务

1. 定义接口
2. 实现服务类
3. 在模块中注册
4. 在主监听器中注入使用

## 10. 总结

当前的监控动态生成流程具有良好的架构设计，但在关联方变更指标处理、性能优化和错误处理方面还有改进空间。建议优先解决关联方变更指标的性能问题和策略模式的完整性问题。
