import { IMetricStrategy } from '../interfaces/metric-strategy.interface';
import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { DynamicDisplayContent } from '../../../../../libs/model/metric/MetricDynamicContentPO';
import { MetricScorePO } from '../../../../../libs/model/metric/MetricScorePO';
import { MetricHitDetailsPO } from '../../../../../libs/model/metric/MetricHitDetailsPO';
import { md5 } from '@kezhaozhao/qcc-utils/dist/qichacha/qichacha.util';

/**
 * 指标策略基类
 * 实现通用逻辑，具体策略类继承此类并实现特定逻辑
 */
export abstract class BaseMetricStrategy implements IMetricStrategy {
  /**
   * 判断是否可以处理特定指标
   * @param metricScorePO 指标得分对象
   * @param dimHitRes 维度命中结果
   */
  public abstract canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean;

  /**
   * 处理指标特定逻辑
   * @param dynamic 动态内容
   * @param dimHitRes 维度命中结果
   * @param batchId 批次ID
   * @param preBatchId 上次批次ID
   */
  public async processMetricIsRepeated(
    dynamic: MonitorMetricDynamicEsDoc,
    dimHitRes: DimensionHitResultPO[],
    batchId: number,
    preBatchId: number,
  ): Promise<[boolean, MonitorMetricDynamicEsDoc]> {
    let isRepeated = true;
    const [totalHits, contents] = await this.compareContent(dynamic, dimHitRes, preBatchId);

    if (totalHits > 0) {
      isRepeated = false;
      dynamic.preBatchId = preBatchId;
      dynamic.metricsContent.displayContent.push(...contents);

      // 重新计算命中策略的命中记录数
      dynamic.metricsContent.metricScorePO.totalHits = totalHits;

      if (dynamic.metricsContent.metricScorePO?.hitDetails) {
        dynamic.metricsContent.metricScorePO.hitDetails = this.resetHitDetails(dynamic.metricsContent.metricScorePO.hitDetails, contents);
      }

      if (dynamic.metricsContent.metricScorePO?.otherHitDetails?.length > 0) {
        dynamic.metricsContent.metricScorePO.otherHitDetails = dynamic.metricsContent.metricScorePO.otherHitDetails.map((hitDetails) => {
          return this.resetHitDetails(hitDetails, contents);
        });
      }
    }

    return [isRepeated, dynamic];
  }

  /**
   * 比对指标内容变化, 返回变化数量和动态内容
   * @param dynamic 动态内容
   * @param dimHitRes 维度命中结果
   * @param preBatchId 上次批次ID
   */
  public abstract compareContent(
    dynamic: MonitorMetricDynamicEsDoc,
    dimHitRes: DimensionHitResultPO[],
    preBatchId: number,
  ): Promise<[number, DynamicDisplayContent[]]>;

  /**
   * 重置命中详情
   * 根据比对结果重新修改metricScorePO中每个策略的命中情况
   * @param hitDetails 命中详情
   * @param contents 内容列表
   */
  protected resetHitDetails(hitDetails: MetricHitDetailsPO, contents: DynamicDisplayContent[]): MetricHitDetailsPO {
    let hitDetailsHits = 0;
    const getCount = (contents: DynamicDisplayContent[], strategyId: number) => {
      return contents.filter((c) => c.strategyId == strategyId).reduce((acc, cur) => acc + cur.count, 0);
    };

    hitDetails?.must?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails?.must_not?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails?.should?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails.totalHits = hitDetailsHits;
    return hitDetails;
  }
}
