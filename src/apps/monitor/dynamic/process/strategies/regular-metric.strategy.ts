import { Injectable } from '@nestjs/common';
import { BaseMetricStrategy } from './base-metric.strategy';
import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { DynamicDisplayContent } from '../../../../../libs/model/metric/MetricDynamicContentPO';
import { MetricScorePO } from '../../../../../libs/model/metric/MetricScorePO';
import * as Bluebird from 'bluebird';
import { SnapshotChangesTypeEnum } from '../../../../diligence/snapshot/po/SearchDimensionDiffsFromSnapshotRequest';
import { DiligenceSnapshotEsCompareService } from '../../../../diligence/snapshot/diligence.snapshot.es.compare.service';
import { DiligenceSnapshotEsService } from '../../../../diligence/snapshot/diligence.snapshot.es.service';

/**
 * 常规指标策略类
 * 处理常规指标的特有逻辑
 */
@Injectable()
export class RegularMetricStrategy extends BaseMetricStrategy {
  constructor(private readonly snapshotEsCompareService: DiligenceSnapshotEsCompareService, private readonly snapshotEsService: DiligenceSnapshotEsService) {
    super();
  }

  /**
   * 判断是否可以处理特定指标
   * 处理除了关联方变更和纳税人资质变更以外的常规指标
   * @param metricScorePO 指标得分对象
   * @param dimHitRes 维度命中结果
   */
  public canHandle(metricScorePO: MetricScorePO, dimHitRes: DimensionHitResultPO[]): boolean {
    // 排除纳税人资质变更指标, 默认的内容比对规则
    return true;
  }

  /**
   * 比对常规指标内容变化
   * @param dynamic 动态内容
   * @param dimHitRes 维度命中结果
   * @param preBatchId 上次批次ID
   */
  public async compareContent(
    dynamic: MonitorMetricDynamicEsDoc,
    dimHitRes: DimensionHitResultPO[],
    preBatchId: number,
  ): Promise<[number, DynamicDisplayContent[]]> {
    let totalHits = 0;
    const displayContents: DynamicDisplayContent[] = [];
    if (!dimHitRes.length) {
      return [totalHits, displayContents];
    }

    const { batchId, companyId, diligenceId, orgId } = dynamic;
    await Bluebird.map(dimHitRes, async (dimHit) => {
      const { dimensionKey, strategyId } = dimHit;
      let dynamicAdd: DynamicDisplayContent;

      if (preBatchId) {
        // 策略的每个维度，比对两次快照的新增数据，动态只存每个维度新增的第一条做示意，totalHits 统计全部的新增数据条数
        const addData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
          dimensionKey: [dimensionKey],
          batchIds: [batchId, preBatchId],
          strategyId,
          recordIds: [],
          companyId: companyId,
          changesType: SnapshotChangesTypeEnum.Added,
          pageSize: 500,
          pageIndex: 1,
        });

        if (addData.total > 0) {
          // 添加常规动态内容
          dynamicAdd = {
            dimensionKey,
            strategyId,
            operate: 0,
            count: addData.total,
            dimensionContent: addData.data.map((d) => d.dimensionContent),
          };
          totalHits += dynamicAdd.count;
          displayContents.push(dynamicAdd);
        }
      } else {
        // 没有上次快照，直接取出本次快照的内容记录为新增动态内容
        // 动态只存每个维度新增的第一条做示意，totalHits 统计全部的新增数据条数
        const addData = await this.snapshotEsService.searchSnapshotData(
          {
            companyId,
            orgId,
            diligenceId: [diligenceId],
            dimensionKey: [dimensionKey],
            pageSize: 1,
            pageIndex: 1,
            strategyId,
          },
          true,
        );

        if (addData.total > 0) {
          // 添加动态内容
          dynamicAdd = {
            dimensionKey,
            strategyId,
            operate: 0,
            count: addData.total,
            dimensionContent: addData.data,
          };
          totalHits += dynamicAdd.count;
          displayContents.push(dynamicAdd);
        }
      }
    });

    return [totalHits, displayContents];
  }

  // /**
  //  * 判断是否含有纳税人资质变更指标
  //  * @param dimHitRes 维度命中结果
  //  */
  // private hasTaxpayerCertificationChange(dimHitRes: DimensionHitResultPO[]): boolean {
  //   return dimHitRes.some((dimHit) => dimHit.dimensionKey == DimensionTypeEnums.TaxpayerCertificationChange);
  // }
}
