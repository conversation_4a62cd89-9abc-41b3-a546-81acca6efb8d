import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as Bluebird from 'bluebird';
import { MonitorMetricsDynamicEntity } from '../../../../../libs/entities/MonitorMetricsDynamicEntity';
import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { DynamicDisplayContent } from '../../../../../libs/model/metric/MetricDynamicContentPO';
import { MetricHitDetailsPO } from '../../../../../libs/model/metric/MetricHitDetailsPO';
import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { MetricStrategyFactory } from '../strategies/metric-strategy.factory';
import * as moment from 'moment';

/**
 * 动态内容比对服务实现
 * 使用策略模式处理不同类型指标
 */
@Injectable()
export class MonitorDynamicComparisonService {
  constructor(
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly dynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    private readonly metricStrategyFactory: MetricStrategyFactory,
  ) {}

  /**
   * 通过比对内容或者hashkey判断不允许重复指标动态是否重复， 返回判定不重复的动态，重复的直接舍弃
   * @param nonRepeatableDynamics 不可重复的动态列表
   */
  public async processNonRepeatableDynamics(nonRepeatableDynamics: MonitorMetricDynamicEsDoc[]): Promise<MonitorMetricDynamicEsDoc[]> {
    const processedDynamics: MonitorMetricDynamicEsDoc[] = [];

    await Bluebird.map(
      nonRepeatableDynamics,
      async (dynamic) => {
        const metricScorePO = dynamic?.metricsContent?.metricScorePO;
        const { isSameMetricStrategy, ignorePlaceholder, lifeCycle } = metricScorePO?.detailsJson?.dynamicStrategy;
        const { companyMetricsHashkey, companyId, orgId, batchId, monitorGroupId } = dynamic;

        // 先查询是否有相同的动态
        const qb = this.dynamicRepo
          .createQueryBuilder('dynamic')
          .select(['dynamic.id', 'dynamic.batchId', 'dynamic.metricsContent'])
          .where('dynamic.monitorGroupId = :monitorGroupId', { monitorGroupId })
          .andWhere('dynamic.companyId = :companyId', { companyId })
          .andWhere('dynamic.orgId = :orgId', { orgId })
          .andWhere('dynamic.companyMetricsHashkey = :companyMetricsHashkey', { companyMetricsHashkey })
          .andWhere('dynamic.batchId != :batchId', { batchId });
        if (ignorePlaceholder) {
          // 忽略占位动态
          qb.andWhere('dynamic.status > -1');
        }
        if (lifeCycle) {
          // 动态的生命周期， 单位天， 从当前时间往前推生命周期天数， 然后取最近的一次动态
          const startDate = moment(new Date()).subtract(lifeCycle, 'days').startOf('date').toDate();
          qb.andWhere('dynamic.createDate > :startDate', { startDate });
        }

        const preMetricDynamic = await qb.orderBy('dynamic.createDate', 'DESC').getOne();

        if (!preMetricDynamic) {
          // 如果没有找到重复的动态，则添加到处理后的列表中
          processedDynamics.push(dynamic);
        } else if (isSameMetricStrategy == 1) {
          // 通过比较两次尽调命中的内容是否完全一致， 一致则不生成动态
          const dimHitRes = this.findMetricDimensions(metricScorePO);

          // 本次维度有命中数据，然后判断上次快照是否有命中数据
          if (dimHitRes.length > 0) {
            const preBatchId = preMetricDynamic?.batchId;

            // 使用策略模式处理指标特定逻辑
            const strategy = this.metricStrategyFactory.getStrategy(metricScorePO, dimHitRes);
            const [isRepeated, dynamic1] = await strategy.processMetricIsRepeated(dynamic, dimHitRes, batchId, preBatchId);

            // 如果内容不重复，加入处理后的动态列表，生成动态
            if (!isRepeated) {
              processedDynamics.push(dynamic1);
            }
          }
        }
      },
      { concurrency: 5 },
    );

    return processedDynamics;
  }

  /**
   * 比对两次动态的具体内容判断动态是否变化
   * 委托给相应的策略类处理
   * @param dynamic 动态内容
   * @param dimHitRes 维度命中结果
   * @param preBatchId 上次批次id
   */
  public async compareDynamicContents(
    dynamic: MonitorMetricDynamicEsDoc,
    dimHitRes: DimensionHitResultPO[],
    preBatchId: number,
  ): Promise<[number, DynamicDisplayContent[]]> {
    const metricScorePO = dynamic.metricsContent.metricScorePO;
    const strategy = this.metricStrategyFactory.getStrategy(metricScorePO, dimHitRes);
    return strategy.compareContent(dynamic, dimHitRes, preBatchId);
  }

  /**
   * 从指标得分中提取维度命中结果
   * @param metircScore 指标得分
   */
  public findMetricDimensions(metircScore: any): DimensionHitResultPO[] {
    const dimHitResults: DimensionHitResultPO[] = [];
    if (metircScore?.totalHits) {
      const array = [];
      array.push(metircScore.hitDetails);
      if (metircScore?.otherHitDetails?.length > 0) {
        array.push(...metircScore.otherHitDetails);
      }
      array.forEach((mhd) => {
        if (mhd?.totalHits) {
          if (mhd?.must?.length) {
            dimHitResults.push(...mhd.must);
          }
          if (mhd?.should?.length) {
            dimHitResults.push(...mhd.should);
          }
          if (mhd?.must_not?.length) {
            dimHitResults.push(...mhd.must_not);
          }
        }
      });
    }
    return dimHitResults.filter((t) => t.dimensionKey);
  }

  /**
   * 重置命中详情
   * 根据比对结果重新修改metricScorePO中每个策略的命中情况
   * @param hitDetails 命中详情
   * @param contents 内容列表
   */
  public resetHitDetails(hitDetails: MetricHitDetailsPO, contents: DynamicDisplayContent[]) {
    let hitDetailsHits = 0;
    const getCount = (contents: DynamicDisplayContent[], strategyId: number) => {
      return contents.filter((c) => c.strategyId == strategyId).reduce((acc, cur) => acc + cur.count, 0);
    };

    hitDetails?.must?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails?.must_not?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails?.should?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });

    hitDetails['totalHits'] = hitDetailsHits;
    return hitDetails;
  }
}
