import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import * as moment from 'moment';
import * as Bluebird from 'bluebird';
import { Cacheable } from 'type-cacheable';
import { DiligenceHistoryEntity } from '../../../../../libs/entities/DiligenceHistoryEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { SnapshotChangesTypeEnum } from '../../../../diligence/snapshot/po/SearchDimensionDiffsFromSnapshotRequest';
import { QueryMonitorDynamicDetialsRequest } from '../../../../../libs/model/monitor/QueryMonitorDynamicDetialsRequest';
import { PaginationResponse } from '@kezhaozhao/qcc-model';
import { DiligenceSnapshotEsService } from '../../../../diligence/snapshot/diligence.snapshot.es.service';
import { DiligenceSnapshotEsCompareService } from '../../../../diligence/snapshot/diligence.snapshot.es.compare.service';

/**
 * 动态详情服务
 * 负责获取动态详情和过滤未监控的公司
 */
@Injectable()
export class MonitorDynamicDetailsService {
  private readonly logger: Logger = QccLogger.getLogger(MonitorDynamicDetailsService.name);

  constructor(
    private readonly snapshotEsService: DiligenceSnapshotEsService,
    private readonly snapshotEsCompareService: DiligenceSnapshotEsCompareService,
    @InjectRepository(MonitorCompanyRelatedPartyEntity) private readonly relatedCompanyRepo: Repository<MonitorCompanyRelatedPartyEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceRepo: Repository<DiligenceHistoryEntity>,
  ) {}

  /**
   * 获取维度命中详情
   * @param param 请求参数
   */
  public async fatchDimensionHitDetails(param: QueryMonitorDynamicDetialsRequest): Promise<PaginationResponse> {
    const { monitorGroupId, companyId, batchId, orgId, diligenceId, preBatchId, dimensionKey, strategyId, pageIndex, pageSize, esFilter, sort } = param;

    // 监控动态，关联监控动态
    if (
      [
        DimensionTypeEnums.ControllerCompany,
        DimensionTypeEnums.AssetInvestigationAndFreezing,
        DimensionTypeEnums.PledgeMerger,
        DimensionTypeEnums.PatentInfo,
        DimensionTypeEnums.RiskChange,
        DimensionTypeEnums.RecentInvestCancellationsRiskChange,
        DimensionTypeEnums.ActualControllerRiskChange,
        DimensionTypeEnums.ListedEntityRiskChange,
      ].includes(dimensionKey)
    ) {
      if (sort?.field) {
        sort.field = 'dimensionContentSearch.publishTime';
      }
      // RiskChange 数据源直接取维度快照数据返回
      return this.snapshotEsService.searchSnapshotData(
        {
          companyId,
          orgId,
          diligenceId: [diligenceId],
          dimensionKey: [dimensionKey],
          pageSize,
          pageIndex,
          strategyId,
          esFilter,
          sort,
        },
        true,
      );
    }

    let result;
    if (preBatchId) {
      // 两次batch对比找到本次新增的记录
      result = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
        dimensionKey: [dimensionKey],
        batchIds: [batchId, preBatchId],
        strategyId,
        recordIds: [],
        companyId: companyId,
        changesType: SnapshotChangesTypeEnum.Added,
        pageSize,
        pageIndex,
        onlyDimension: 1,
        esFilter,
      });
    } else {
      // 没有上次快照，直接取出本次快照的内容记录为新增动态内容
      result = await this.snapshotEsService.searchSnapshotData(
        {
          companyId,
          orgId,
          diligenceId: [diligenceId],
          dimensionKey: [dimensionKey],
          pageSize,
          pageIndex,
          strategyId,
          esFilter,
          // 关联方列表不支持排序
          // sort,
        },
        true,
      );
    }

    // 关联方变更列表展示时，获取最新的监控状态
    if (DimensionTypeEnums.RelatedCompanyChange == dimensionKey) {
      if (result.data.length > 0) {
        // 标记是否已被监控
        // 关联方列表中已经被加入关联方的企业打上标签
        const companyKeynoRelatedList = result.data.map((x) => x?.companyKeynoRelated);
        const monitored = await this.relatedCompanyRepo.find({
          where: {
            monitorGroupId,
            companyIdRelated: In(companyKeynoRelatedList),
            companyIdPrimary: companyId,
          },
        });

        result.data.forEach((x) => {
          x['isMonitor'] = !!monitored.find((m) => m.companyIdRelated === x?.companyKeynoRelated);
        });
      }
    }

    // 查询纳税人资质变更
    if (DimensionTypeEnums.TaxpayerCertificationChange === dimensionKey) {
      if (result.data.length > 0 && preBatchId) {
        const removeData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
          dimensionKey: [dimensionKey],
          batchIds: [batchId, preBatchId],
          strategyId,
          recordIds: [],
          companyId: companyId,
          changesType: SnapshotChangesTypeEnum.Removed,
          pageSize,
          pageIndex,
          onlyDimension: 1,
          esFilter,
        });
        const diligenceHistoryEntity = await this.diligenceRepo.findOne({
          where: {
            id: diligenceId,
          },
        });
        result.data = [
          {
            changeBefore: removeData?.data[0]?.description || '-',
            changeAfter: result?.data[0]?.description || '-',
            changeDate: moment(diligenceHistoryEntity?.createDate).format('YYYY-MM-DD') || '-',
            CreateDate: moment(diligenceHistoryEntity?.createDate).startOf('day').valueOf() / 1000,
          },
        ];
      }
    }

    return result;
  }

  /**
   * 过滤未监控的公司
   * @param request 查询请求
   */
  @Cacheable({ ttlSeconds: 60 })
  public async filterUnMonitorCompany(request: QueryMonitorDynamicDetialsRequest): Promise<{
    unMonitorCompanyCount: number;
    filterMonitorCompanyCount: number;
    unMonitorCompanyList: any[];
    unsupportedCompanies: any[];
  }> {
    //分页取所有
    const pageSize = 2000; // 每批次查询的最大条数
    let pageIndex = 1; // 当前查询的起始位置
    let companyRelatedList = [];
    let total = 0;

    do {
      request.pageIndex = pageIndex;
      request.pageSize = pageSize;
      //获取所有关联方企业 快照中已剔除不支持的企业
      const resp: PaginationResponse = await this.fatchDimensionHitDetails(request);
      companyRelatedList = companyRelatedList.concat(resp.data);
      total = resp.total;
      // 增加页码，准备下一次查询
      pageIndex++;
    } while ((pageIndex - 1) * pageSize < total);

    // 过滤未监控的公司
    const unMonitorCompanyList = companyRelatedList.filter((x) => x['isMonitor'] === false);

    if (!companyRelatedList) {
      return {
        unMonitorCompanyCount: 0,
        filterMonitorCompanyCount: 0,
        unMonitorCompanyList: [],
        unsupportedCompanies: [],
      };
    }
    const unsupportedCompanies: any[] = [];
    return {
      unMonitorCompanyCount: unMonitorCompanyList?.length,
      filterMonitorCompanyCount: unMonitorCompanyList?.length - unsupportedCompanies?.length,
      unMonitorCompanyList: unMonitorCompanyList,
      unsupportedCompanies: unsupportedCompanies,
    };
  }
}
