import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { v4 } from 'uuid';
import * as Bluebird from 'bluebird';
import { find, flattenDeep, toPlainObject } from 'lodash';
import { DiligenceHistoryEntity } from '../../../../../libs/entities/DiligenceHistoryEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorMetricDynamicEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { CompanyRelatedPartyEsDoc } from '../../../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { MetricService } from '../../../../metric/metric.service';
import { MonitorDynamicComparisonService } from './monitor-dynamic-comparison.service';
import { MonitorDynamicDetailsService } from './monitor-dynamic-details.service';
import { getMetricsDynamicSnapshotId, findRelatedStreategyIds } from '../../../monitor.utils';
import { ProductCodeEnums } from '../../../../../libs/enums/ProductCodeEnums';
import { DynamicDisplayContent } from '../../../../../libs/model/metric/MetricDynamicContentPO';
import { DimensionHitResultPO } from '../../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { PaginationResponse } from '@kezhaozhao/qcc-model';
import { MonitorCompanyRelatedStatusEnum } from '../../../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import { RelatedPartyGroupPO } from '../../../../../libs/model/diligence/graph/RelatedPartyGroupPO';
import { MonitorDynamicRelatedService } from './monitor-dynamic-related.service';

// 定义metricFeature的类型，避免类型错误
interface MetricFeature {
  metricsId: number;
  metricsName?: string;
  featureKeyL1?: string;
  featureKeyL2?: string;
}

// 关联方变更数据接口
interface RelatedChangeData {
  dimHit: DimensionHitResultPO;
  searchRes: PaginationResponse;
  operationType: number;
  targetStatus: MonitorCompanyRelatedStatusEnum;
}

/**
 * 动态生成服务
 * 负责生成不同类型的动态
 */
@Injectable()
export class MonitorDynamicGenerationService {
  private readonly logger: Logger = QccLogger.getLogger(MonitorDynamicGenerationService.name);

  constructor(
    private readonly metricService: MetricService,
    private readonly comparisonService: MonitorDynamicComparisonService,
    private readonly detailsService: MonitorDynamicDetailsService,
    private readonly relatedService: MonitorDynamicRelatedService,
  ) {}

  /**
   * 生成占位动态
   * @param finshedDiligence 已完成的尽调记录
   * @param initDynamics 初始化动态
   * @param primaryCompanies 主体公司
   * @param monitorGroupId 监控分组ID
   * @param batchId 批次ID
   * @param orgId 组织ID
   * @param product 产品代码
   */
  public generatePlaceholderDynamics(
    finshedDiligence: DiligenceHistoryEntity[],
    initDynamics: any[],
    primaryCompanies: MonitorCompanyRelatedPartyEntity[],
    monitorGroupId: number,
    batchId: number,
    orgId: number,
    product: ProductCodeEnums,
  ): MonitorMetricDynamicEsDoc[] {
    const placeholderDynamics = finshedDiligence
      .map((d) => {
        if (!d.details?.originalHits?.length) {
          const fakeMetricsId = -1;
          const companyMetricsHashkey = getMetricsDynamicSnapshotId({
            monitorGroupId,
            modelBranchCode: d.modelBranchCode,
            metricsId: fakeMetricsId,
            riskLevel: d.result,
            companyId: d.companyId,
          });
          return {
            metricsId: fakeMetricsId,
            metricsName: '公司加入监控分组第一次排查没有风险，用于占位',
            riskLevel: d.result,
            riskScore: d.score,
            riskModelId: d.orgModelId,
            riskModelBranchCode: d.modelBranchCode,
            relatedCompany: this.generateRelatedPartyEsDoc(primaryCompanies, d.companyId),
            monitorGroupId,
            companyId: d.companyId,
            companyName: d.name,
            uniqueHashkey: v4(),
            diligenceId: d.id,
            diligenceResult: d.result,
            diligenceScore: d.score,
            totalHits: 0,
            metricsContent: null,
            status: -2,
            batchId,
            preBatchId: 0,
            companyMetricsHashkey,
            orgId,
            product,
            createDate: new Date(),
          } as MonitorMetricDynamicEsDoc;
        }
        return null;
      })
      .filter((t): t is MonitorMetricDynamicEsDoc => t !== null && !initDynamics.some((e) => e.companyId == t.companyId));

    return placeholderDynamics;
  }

  /**
   * 分类指标动态
   * 这里是所有命中的指标，所以需要同时也筛选出来哪些是用来占位的
   * @param finshedDiligence 已完成的尽调记录
   * @param initDynamics 初始化动态
   * @param primaryCompanies 主体公司
   * @param monitorGroupId 监控分组ID
   * @param batchId 批次ID
   * @param orgId 组织ID
   * @param product 产品代码
   */
  public async classifyMetricDynamics(
    finshedDiligence: DiligenceHistoryEntity[],
    initDynamics: any[],
    primaryCompanies: MonitorCompanyRelatedPartyEntity[],
    monitorGroupId: number,
    batchId: number,
    orgId: number,
    product: ProductCodeEnums,
    placeholderDynamics: MonitorMetricDynamicEsDoc[],
  ): Promise<{ repeatableDynamics: MonitorMetricDynamicEsDoc[]; nonRepeatableDynamics: MonitorMetricDynamicEsDoc[] }> {
    const repeatableDynamics: MonitorMetricDynamicEsDoc[] = [];
    const nonRepeatableDynamics: MonitorMetricDynamicEsDoc[] = [];
    const allMetricIds = flattenDeep(finshedDiligence.map((d) => d.details?.originalHits?.map((h) => h?.metricsId))).filter((t) => t);

    // 异步获取指标特征
    let metricFeatures: MetricFeature[] = [];
    try {
      metricFeatures = (await this.metricService.getMetricFeature(allMetricIds, 2)) as unknown as MetricFeature[];
    } catch (err) {
      this.logger.error(`获取指标特征失败: ${err.message}`);
    }

    finshedDiligence.forEach((d) => {
      if (d.details?.originalHits?.length) {
        d.details.originalHits.forEach((metricScorePO) => {
          const companyMetricsHashkey = getMetricsDynamicSnapshotId({
            monitorGroupId,
            modelBranchCode: d.modelBranchCode,
            riskLevel: metricScorePO.riskLevel,
            metricsId: metricScorePO.metricsId,
            companyId: d.companyId,
          });
          const status = initDynamics.some((e) => e.companyId == d.companyId) ? 0 : -1;
          const metricFeatureObj = find(metricFeatures, { metricsId: metricScorePO.metricsId });

          const dynamicPO = {
            createDate: new Date(),
            uniqueHashkey: v4(),
            metricsId: metricScorePO.metricsId,
            metricsName: metricFeatureObj?.metricsName || metricScorePO.name,
            metricsFeatureKeyL1: metricFeatureObj?.featureKeyL1,
            metricsFeatureKeyL2: metricFeatureObj?.featureKeyL2,
            relatedStrategyIds: findRelatedStreategyIds(metricScorePO.hitDetails),
            relatedCompany: this.generateRelatedPartyEsDoc(primaryCompanies, d.companyId),
            metricsType: metricScorePO.metricType,
            riskLevel: metricScorePO.riskLevel,
            riskScore: metricScorePO.score,
            riskModelId: d.orgModelId,
            riskModelBranchCode: d.modelBranchCode,
            monitorGroupId,
            companyId: d.companyId,
            companyName: d.name,
            diligenceId: d.id,
            diligenceResult: d.result,
            diligenceScore: d.score,
            metricsContent: {
              metricScorePO: toPlainObject(metricScorePO),
              displayContent: [],
            },
            status,
            batchId,
            preBatchId: 0,
            companyMetricsHashkey,
            orgId,
            product,
          } as MonitorMetricDynamicEsDoc;

          if (dynamicPO.status == -1) {
            // 未初始化过基线动态的企业，需要先占位
            placeholderDynamics.push(dynamicPO);
          } else if (metricScorePO.detailsJson?.dynamicStrategy?.allowRepeatedHits) {
            repeatableDynamics.push(dynamicPO);
          } else {
            nonRepeatableDynamics.push(dynamicPO);
          }
        });
      }
    });

    return { repeatableDynamics, nonRepeatableDynamics };
  }

  /**
   * 填充动态内容 - 优化版本
   * 使用职责分离和策略模式重构
   * @param dynamicList 动态列表
   */
  public async fulfillDynamicContent(dynamicList: MonitorMetricDynamicEsDoc[]): Promise<MonitorMetricDynamicEsDoc[]> {
    return Bluebird.map(
      dynamicList,
      async (dynamic) => {
        // 对初始化动态和已经有内容的动态不需要处理直接返回
        if (!this.shouldProcessDynamic(dynamic)) {
          return dynamic;
        }
        // 处理单个动态，给动态填充内容
        const metricScorePO = dynamic.metricsContent.metricScorePO;
        // 找到维度命中结果
        const dimHitRes = this.comparisonService.findMetricDimensions(metricScorePO);

        if (dimHitRes.length === 0) {
          return dynamic;
        }

        // 根据指标类型选择处理方式
        if (this.relatedService.isRelatedMetric(dimHitRes)) {
          // 处理关联方变更指标
          await this.processRelatedCompanyMetric(dynamic, dimHitRes);
        } else {
          // 处理常规指标
          await this.processRegularMetric(dynamic, dimHitRes);
        }
        return dynamic;
      },
      { concurrency: 10 },
    );
  }

  /**
   * 判断是否需要处理动态，对初始化动态和已经有内容的动态不需要处理
   */
  private shouldProcessDynamic(dynamic: MonitorMetricDynamicEsDoc): boolean {
    return ![-1, -2].includes(dynamic.status) && dynamic.metricsContent.displayContent.length < 1;
  }

  /**
   * 处理关联方变更指标
   */
  private async processRelatedCompanyMetric(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[]): Promise<void> {
    const { companyId, monitorGroupId, uniqueHashkey } = dynamic;

    // 设置关联方变更标记
    await this.relatedService.setRelatedDynamicHashKey([companyId], monitorGroupId, uniqueHashkey);

    // 批量获取关联方变更数据
    const relatedChangesData = await this.batchFetchRelatedChanges(dynamic, dimHitRes);

    // 批量更新关联方状态
    await this.batchUpdateRelatedStatus(dynamic, relatedChangesData);

    // 生成关联方动态内容
    const relatedChanges = relatedChangesData
      .filter((data) => data.searchRes?.total > 0)
      .map((data) => this.createDynamicContent(data.dimHit, data.searchRes, data.operationType, data.searchRes.data.slice(0, 1)));

    // 添加动态内容
    dynamic.metricsContent.displayContent.push(...relatedChanges);
  }

  /**
   * 处理常规指标
   */
  private async processRegularMetric(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[]): Promise<void> {
    const firstDimHit = dimHitRes[0];
    const searchRes = await this.fetchDimensionDetails(dynamic, firstDimHit, 1);

    if (searchRes?.total > 0) {
      const dynamicContent = this.createDynamicContent(firstDimHit, searchRes, 0);
      dynamic.metricsContent.displayContent.push(dynamicContent);
    }
  }

  /**
   * 批量获取关联方变更数据
   */
  private async batchFetchRelatedChanges(dynamic: MonitorMetricDynamicEsDoc, dimHitRes: DimensionHitResultPO[]): Promise<RelatedChangeData[]> {
    return Bluebird.map(
      dimHitRes,
      async (dimHit) => {
        const searchRes = await this.fetchDimensionDetails(dynamic, dimHit, dimHit.totalHits);
        return {
          dimHit,
          searchRes,
          operationType: dimHit.strategyName === '关联方新增' ? 0 : 1,
          targetStatus: dimHit.strategyName === '关联方新增' ? MonitorCompanyRelatedStatusEnum.Invalid : MonitorCompanyRelatedStatusEnum.Valid,
        };
      },
      { concurrency: 3 },
    );
  }

  /**
   * 批量更新关联方状态
   */
  private async batchUpdateRelatedStatus(dynamic: MonitorMetricDynamicEsDoc, relatedChangesData: RelatedChangeData[]): Promise<void> {
    const { companyId, monitorGroupId } = dynamic;

    // 按状态分组
    const statusGroups = relatedChangesData
      .filter((data) => data.searchRes?.total > 0)
      .reduce((groups, data) => {
        const status = data.targetStatus;
        groups[status] = groups[status] || [];
        groups[status].push(...data.searchRes.data);
        return groups;
      }, {} as Record<string, RelatedPartyGroupPO[]>);

    // 并行更新不同状态
    await Promise.all(
      Object.entries(statusGroups).map(([status, companies]) => this.relatedService.updateRelated(companies, monitorGroupId, companyId, status as any)),
    );
  }

  /**
   * 获取维度详情
   */
  private async fetchDimensionDetails(dynamic: MonitorMetricDynamicEsDoc, dimHit: DimensionHitResultPO, pageSize: number): Promise<PaginationResponse> {
    const { companyId, orgId, diligenceId, monitorGroupId } = dynamic;

    return this.detailsService.fatchDimensionHitDetails({
      companyId,
      orgId,
      dimensionKey: dimHit.dimensionKey,
      diligenceId,
      strategyId: dimHit.strategyId,
      pageSize,
      pageIndex: 1,
      batchId: null,
      preBatchId: null,
      monitorGroupId,
      sort: { field: 'publishTime', order: 'DESC' },
    });
  }

  /**
   * 创建动态内容
   */
  private createDynamicContent(dimHit: DimensionHitResultPO, searchRes: PaginationResponse, operate: number, dimensionContent?: any[]): DynamicDisplayContent {
    return {
      dimensionKey: dimHit.dimensionKey,
      strategyId: dimHit.strategyId,
      operate,
      count: searchRes.total,
      dimensionContent: dimensionContent || searchRes.data,
    };
  }

  /**
   * 根据指定companyId，找到他是谁的关联方并生成 es doc
   * @param relatedCompanies 关联方列表
   * @param companyId 公司ID
   */
  private generateRelatedPartyEsDoc(relatedCompanies: MonitorCompanyRelatedPartyEntity[], companyId: string): CompanyRelatedPartyEsDoc[] {
    return relatedCompanies
      .filter((r) => r.companyIdRelated == companyId)
      .map((b) => {
        return {
          companyIdPrimary: b.companyIdPrimary,
          relatedType: b.relatedType,
        };
      });
  }
}
