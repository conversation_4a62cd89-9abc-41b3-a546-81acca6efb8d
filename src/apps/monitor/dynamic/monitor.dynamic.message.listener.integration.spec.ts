// import { Test, TestingModule } from '@nestjs/testing';
// import { EntityManager, getConnection, getRepository, Repository } from 'typeorm';
// import { TypeOrmModule } from '@nestjs/typeorm';
// import { MonitorDynamicMessageListener } from './monitor.dynamic.message.listener';
// import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
// import { MonitorMetricsDynamicEntity } from '../../../libs/entities/MonitorMetricsDynamicEntity';
// import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
// import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
// import { MonitorCompanyRelatedPartyEntity } from '../../../libs/entities/MonitorCompanyRelatedPartyEntity';
// import { InjectRepository } from '@nestjs/typeorm';
// import { AppTestModule } from '../../app/app.test.module';
// import { MonitorModule } from '../monitor.module';
// import { QueueService } from '../../../libs/config/queue.service';
// import { MonitorDynamicEsService } from './monitor.dynamic.es.service';
// import { DiligenceSnapshotEsCompareService } from '../../diligence/snapshot/diligence.snapshot.es.compare.service';
// import { DiligenceSnapshotEsService } from '../../diligence/snapshot/diligence.snapshot.es.service';
// import { MetricService } from '../../metric/metric.service';
// import { AnalyzeMonitorDynamicMessagePO } from '../po/AnalyzeMonitorDynamicMessagePO';
// import { PlatformUser } from '../../../libs/model/common';
// import { ProductCodeEnums } from '../../../libs/enums/ProductCodeEnums';
// import { clearMonitorTestData } from '../../test_utils_module/monitor.test.tools';
// import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
// import { QueryMonitorDynamicDetialsRequest } from '../../../libs/model/monitor/QueryMonitorDynamicDetialsRequest';
// import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
// import { SnapshotChangesTypeEnum } from '../../diligence/snapshot/po/SearchDimensionDiffsFromSnapshotRequest';
// import { SnapshotStatus } from '../../../libs/model/diligence/SnapshotDetail';
// import { MetricsEntity } from '../../../libs/entities/MetricsEntity';
// import { MetricDimensionRelationEntity } from '../../../libs/entities/MetricDimensionRelationEntity';
// import { GroupEntity } from '../../../libs/entities/GroupEntity';
// import { DiligenceSnapshotHelper } from '../../diligence/snapshot/diligence.snapshot.helper';
// import { ModelInitMonitorService } from '../../risk_model/init_mode/model.init.monitor.service';
// import { RiskModelService } from '../../risk_model/risk_model.service';
// import { RiskModelEntity } from 'libs/entities/RiskModelEntity';
// import { DistributedSystemResourceEntity } from '../../../libs/entities/DistributedSystemResourceEntity';
// import { clearAllTestRiskModelTestData } from '../../test_utils_module/riskmodel.test.utils';
// import { DistributedResourceTypeEnums } from '../../../libs/enums/DistributedResourceTypeEnums';

// jest.setTimeout(600000);
// describe('MonitorDynamicMessageListener集成测试', () => {
//   const [testOrgId, testUserId] = generateUniqueTestIds('monitor.dynamic.message.listener.integration.spec.ts');
//   const testUser = getTestUser(testOrgId, testUserId, ProductCodeEnums.Pro);

//   let module: TestingModule;
//   let monitorDynamicMessageListener: MonitorDynamicMessageListener;
//   let entityManager: EntityManager;
//   let relatedCompanyRepo: Repository<MonitorCompanyRelatedPartyEntity>;
//   let queueService: QueueService;
//   let snapshotEsCompareService: DiligenceSnapshotEsCompareService;
//   let snapshotEsService: DiligenceSnapshotEsService;

//   beforeAll(async () => {
//     module = await Test.createTestingModule({
//       imports: [
//         AppTestModule,
//         TypeOrmModule.forFeature([
//           DiligenceHistoryEntity,
//           MonitorMetricsDynamicEntity,
//           MonitorGroupEntity,
//           MonitorCompanyEntity,
//           MonitorCompanyRelatedPartyEntity,
//           MetricsEntity,
//           MetricDimensionRelationEntity,
//           GroupEntity,
//           RiskModelEntity,
//           DistributedSystemResourceEntity,
//         ]),
//       ],
//       providers: [
//         ModelInitMonitorService,
//         RiskModelService,
//         MonitorDynamicMessageListener,
//         MonitorDynamicEsService,
//         DiligenceSnapshotEsCompareService,
//         DiligenceSnapshotEsService,
//         MetricService,
//       ],
//     }).compile();

//     monitorDynamicMessageListener = module.get<MonitorDynamicMessageListener>(MonitorDynamicMessageListener);

//     relatedCompanyRepo = getRepository(MonitorCompanyRelatedPartyEntity);
//     entityManager = relatedCompanyRepo.manager;
//     ``;
//     queueService = module.get<QueueService>(QueueService);
//     snapshotEsCompareService = module.get<DiligenceSnapshotEsCompareService>(DiligenceSnapshotEsCompareService);
//     snapshotEsService = module.get<DiligenceSnapshotEsService>(DiligenceSnapshotEsService);
//   });

//   beforeEach(async () => {
//     // 清理测试数据
//     await clearMonitorTestData(entityManager, testUser);
//     jest.clearAllMocks();
//   });

//   afterEach(async () => {
//     // 清理测试数据
//     await clearMonitorTestData(entityManager, testUser);
//     jest.clearAllMocks();
//   });

//   afterAll(async () => {
//     await clearMonitorTestData(entityManager, testUser);
//     const connection = getConnection();
//     await connection.close();
//   });

//   describe('handleMetricsAnalyze测试', () => {
//     it('当没有找到尽调记录时应返回零记录', async () => {
//       // 准备测试数据
//       const msgPO: AnalyzeMonitorDynamicMessagePO = {
//         orgId: testUser.currentOrg,
//         product: testUser.currentProduct,
//         monitorGroupId: 1,
//         diligenceIds: [9999], // 不存在的尽调ID
//         batchId: 1,
//         retryCount: 0,
//       };

//       // Mock findDiligence方法返回空数组
//       jest.spyOn(monitorDynamicMessageListener as any, 'findDiligence').mockResolvedValue([]);

//       // 执行测试方法
//       const result = await monitorDynamicMessageListener.handleMetricsAnalyze(msgPO);

//       // 验证结果
//       expect(result).toEqual({
//         placeholderCount: 0,
//         repeatableCount: 0,
//         nonRepeatableCount: 0,
//         totalCount: 0,
//       });
//     });

//     it('当有未完成的尽调快照时应重新发送消息', async () => {
//       // 准备测试数据
//       const msgPO: AnalyzeMonitorDynamicMessagePO = {
//         orgId: testUser.currentOrg,
//         product: testUser.currentProduct,
//         monitorGroupId: 1,
//         diligenceIds: [1, 2], // 存在的尽调ID
//         batchId: 1,
//         retryCount: 0,
//       };

//       // Mock findDiligence方法返回包含处理中的尽调记录
//       jest.spyOn(monitorDynamicMessageListener as any, 'findDiligence').mockResolvedValue([
//         {
//           id: 1,
//           snapshotDetails: { status: SnapshotStatus.PROCESSING }, // 处理中的尽调
//           companyId: 'company1',
//           name: '测试公司1',
//         },
//       ]);

//       // Mock sendMessageV2方法
//       const sendMessageMock = jest.spyOn(queueService.continuousDiligenceAnalyzeQueue, 'sendMessageV2').mockResolvedValue({} as any);

//       // 执行测试方法
//       const result = await monitorDynamicMessageListener.handleMetricsAnalyze(msgPO);

//       // 验证结果
//       expect(result).toEqual({
//         placeholderCount: 0,
//         repeatableCount: 0,
//         nonRepeatableCount: 0,
//         totalCount: 0,
//       });

//       // 验证是否调用了sendMessageV2
//       expect(sendMessageMock).toHaveBeenCalledWith(
//         expect.objectContaining({
//           diligenceIds: [1],
//           retryCount: 1,
//         }),
//         expect.objectContaining({ ttl: 60 * 1000 }),
//       );
//     });
//   });

//   describe('fatchDimensionHitDetails测试', () => {
//     it('当维度类型为RiskChange时应直接返回维度快照数据', async () => {
//       // 准备测试数据
//       const request: QueryMonitorDynamicDetialsRequest = {
//         monitorGroupId: 1,
//         companyId: 'company1',
//         batchId: 1,
//         orgId: testUser.currentOrg,
//         diligenceId: 1,
//         dimensionKey: DimensionTypeEnums.RiskChange,
//         pageIndex: 1,
//         pageSize: 10,
//         strategyId: 1,
//         preBatchId: 0,
//       };

//       // Mock searchSnapshotData方法
//       const mockResult = {
//         data: [{ id: 1, content: '测试内容' } as any],
//         pageIndex: 1,
//         pageSize: 10,
//         total: 1,
//       };
//       jest.spyOn(snapshotEsService, 'searchSnapshotData').mockResolvedValue(mockResult);

//       // 执行测试方法
//       const result = await monitorDynamicMessageListener.fatchDimensionHitDetails(request);

//       // 验证结果
//       expect(result).toEqual(mockResult);
//       expect(snapshotEsService.searchSnapshotData).toHaveBeenCalledWith(
//         expect.objectContaining({
//           companyId: 'company1',
//           orgId: testUser.currentOrg,
//           diligenceId: [1],
//           dimensionKey: [DimensionTypeEnums.RiskChange],
//         }),
//         true,
//       );
//     });

//     it('当有preBatchId时应比较两次batch的差异', async () => {
//       // 准备测试数据
//       const request: QueryMonitorDynamicDetialsRequest = {
//         monitorGroupId: 1,
//         companyId: 'company1',
//         batchId: 2,
//         preBatchId: 1,
//         orgId: testUser.currentOrg,
//         diligenceId: 1,
//         dimensionKey: DimensionTypeEnums.RelatedCompanies, // 使用合法的枚举值
//         pageIndex: 1,
//         pageSize: 10,
//         strategyId: 1,
//       };

//       // Mock searchDimensionDiffsByBatch方法
//       const mockResult = {
//         data: [{ id: 1, content: '测试差异内容' } as any],
//         pageIndex: 1,
//         pageSize: 10,
//         total: 1,
//       };
//       jest.spyOn(snapshotEsCompareService, 'searchDimensionDiffsByBatch').mockResolvedValue(mockResult);

//       // 执行测试方法
//       const result = await monitorDynamicMessageListener.fatchDimensionHitDetails(request);

//       // 验证结果
//       expect(result).toEqual(mockResult);
//       expect(snapshotEsCompareService.searchDimensionDiffsByBatch).toHaveBeenCalledWith(
//         expect.objectContaining({
//           dimensionKey: [DimensionTypeEnums.RelatedCompanyChange],
//           batchIds: [2, 1],
//           companyId: 'company1',
//           changesType: SnapshotChangesTypeEnum.Added,
//         }),
//       );
//     });

//     it('当维度类型为RelatedCompanies时应标记已被监控的公司', async () => {
//       // 准备测试数据
//       const request: QueryMonitorDynamicDetialsRequest = {
//         monitorGroupId: 1,
//         companyId: 'company1',
//         batchId: 1,
//         orgId: testUser.currentOrg,
//         diligenceId: 1,
//         dimensionKey: DimensionTypeEnums.RelatedCompanies,
//         pageIndex: 1,
//         pageSize: 10,
//         strategyId: 1,
//         preBatchId: 0,
//       };

//       // Mock searchSnapshotData方法返回关联公司数据
//       const mockResult = {
//         data: [{ companyKeynoRelated: 'related1' } as any, { companyKeynoRelated: 'related2' } as any],
//         pageIndex: 1,
//         pageSize: 10,
//         total: 2,
//       };
//       jest.spyOn(snapshotEsService, 'searchSnapshotData').mockResolvedValue(mockResult);

//       // Mock relatedCompanyRepo.find方法
//       const mockRelatedCompany = new MonitorCompanyRelatedPartyEntity();
//       mockRelatedCompany.companyIdRelated = 'related1';
//       mockRelatedCompany.companyIdPrimary = 'company1';
//       mockRelatedCompany.monitorGroupId = 1;

//       jest.spyOn(relatedCompanyRepo, 'find').mockResolvedValue([mockRelatedCompany]);

//       // 执行测试方法
//       const result = await monitorDynamicMessageListener.fatchDimensionHitDetails(request);

//       // 验证结果
//       expect(result.data[0].isMonitor).toBe(true); // related1应该被标记为已监控
//       expect(result.data[1].isMonitor).toBe(false); // related2应该被标记为未监控
//     });
//   });
// });
