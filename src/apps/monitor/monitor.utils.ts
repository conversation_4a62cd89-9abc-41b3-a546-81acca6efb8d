import { md5 } from '@kezhaozhao/qcc-utils/dist/qichacha/qichacha.util';
import { MetricHitDetailsPO } from '../../libs/model/metric/MetricHitDetailsPO';
import { MetricScorePO } from 'libs/model/metric/MetricScorePO';

/**
 * 指标动态保存es的时候的id
 * @param params
 */
export const getMetricsDynamicSnapshotId = (params: {
  monitorGroupId: number;
  metricsId: number;
  companyId: string;
  modelBranchCode: string;
  riskLevel: number;
  isSameMetricStrategy?: number; // 对应 MetricDynamicStrategy.isSameMetricStrategy , 如果没传值，默认就用 0
}): string => {
  const baseStr = `metric_snapshot_${params.monitorGroupId}_${params.companyId}_${params.metricsId}_${params.modelBranchCode}`;
  return md5(baseStr);
};

/**
 * 手工生成的唯一键:
 * 如果是占位用的，则用companyMetricsHashkey+status 组成的hash，
 * 如果是可重复出现的key，那就用 companyMetricsHashkey+ 之间时间间隔内的唯一ID
 * @param params
 */
export const generateMetricsDynamicUniqueKey = (params: {
  companyMetricsHashkey: string;
  dataStatus: -1 | -2 | 0; // 1 占位用， 2 非占位用
}): string => {
  if (params.dataStatus < 0) {
    // 占位用的， 用 companyMetricsHashkey+status 组成的hash
    return md5(`${params.companyMetricsHashkey}_${params.dataStatus}`);
  }
  return md5(`${params.companyMetricsHashkey}_${generateTimeRangeID(15)}`);
};

/**
 * 基于时间生成id， 在指定时间范围内， 生成的id是一样的
 * @param range
 */
export const generateTimeRangeID = (range?: number) => {
  range = range || 5;
  const now = new Date();
  const timestamp = now.getTime(); // 获取当前时间的时间戳（毫秒）

  // 将时间戳向下取整到5分钟的倍数
  const fiveMinutes = range * 60 * 1000; // 5分钟的毫秒数
  const roundedTimestamp = Math.floor(timestamp / fiveMinutes) * fiveMinutes;

  // 生成ID，可以使用时间戳的字符串形式或其他格式
  const id = `ID_${roundedTimestamp}`;
  return id;
};

export const findRelatedStreategyIds = (hitDetails: MetricHitDetailsPO) => {
  if (!hitDetails) {
    return [];
  }
  const set: Set<number> = new Set();
  const strategy = hitDetails.hitStrategy;
  strategy.must?.forEach((f) => {
    set.add(f);
  });
  strategy.should?.forEach((f) => {
    set.add(f);
  });
  strategy.must_not?.forEach((f) => {
    set.add(f);
  });
  return Array.from(set);
};

export const findAllStrategyIds = (metricScorePO: MetricScorePO) => {
  if (!metricScorePO) {
    return new Set<number>();
  }
  const { hitDetails, otherHitDetails } = metricScorePO;
  const array: number[] = [];
  if (!hitDetails) {
    array.push(...findRelatedStreategyIds(hitDetails));
  }
  if (otherHitDetails?.length) {
    otherHitDetails.forEach((h) => {
      array.push(...findRelatedStreategyIds(h));
    });
  }
  return new Set(array);
};
