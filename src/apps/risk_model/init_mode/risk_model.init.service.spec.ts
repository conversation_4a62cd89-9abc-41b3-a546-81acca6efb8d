import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../app/app.test.module';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import { EntityManager, getConnection, In } from 'typeorm';
import { clearAllTestRiskModelTestData } from '../../test_utils_module/riskmodel.test.utils';
import { DistributedResourceTypeEnums } from '../../../libs/enums/DistributedResourceTypeEnums';
import { RiskModelModule } from '../risk_model.module';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { DistributedSystemResourceEntity } from '../../../libs/entities/DistributedSystemResourceEntity';
import { GroupMetricRelationEntity } from '../../../libs/entities/GroupMetricRelationEntity';
import { MetricDimensionRelationEntity } from '../../../libs/entities/MetricDimensionRelationEntity';
import { RiskModelInitService } from './risk_model.init.service';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { GroupEntity } from '../../../libs/entities/GroupEntity';
import * as _ from 'lodash';
import { RiskModelICBCSZService } from './risk_model.init.icbc.sz.service';

/**
 * 风险模型
 */
jest.setTimeout(60000);
describe('RiskModelInitService Test', () => {
  let riskModelInitService: RiskModelInitService;
  let riskModelICBCSZService: RiskModelICBCSZService;
  let entityManager: EntityManager;
  let result: RiskModelEntity;
  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('risk_model.init.service.spec.ts');
  console.log('test data');
  console.log('testOrgId', testOrgId);
  console.log('testUserId', testUserId);
  console.log('test data');
  const testUser = getTestUser(testOrgId, testUserId);
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, RiskModelModule],
    }).compile();
    riskModelInitService = module.get<RiskModelInitService>(RiskModelInitService);
    riskModelICBCSZService = module.get<RiskModelICBCSZService>(RiskModelICBCSZService);
    entityManager = module.get<EntityManager>(EntityManager);
  });
  afterEach(async () => {
    await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel, []);
  });
  afterAll(async () => {
    const connection = getConnection();
    await connection.close();
  });

  describe('createHTFModel 方法测试', () => {
    it('应该成功创建HTF风险模型并正确设置所有相关数据', async () => {
      // Given
      const modelName = '【尽调】(汇添富)测试模型';
      const modelType = RiskModelTypeEnums.GeneralRiskModel;

      // When
      result = await riskModelInitService.createHTFModel(testUser, modelName, testUser.currentOrg, modelType);

      // Then
      // 1. 验证基本属性
      expect(result).toBeDefined();
      expect(result.modelId).toBeGreaterThan(0);
      const savedModel = await entityManager.findOne(RiskModelEntity, {
        where: { modelId: result.modelId },
      });
      expect(savedModel).toBeDefined();
      expect(savedModel.orgId).toBe(testOrgId);
      expect(savedModel.status).toBe(DataStatusEnums.Developing);

      // 2. 验证分发资源记录
      const distributedResource = await entityManager.findOne(DistributedSystemResourceEntity, {
        where: {
          resourceId: result.modelId,
          resourceType: DistributedResourceTypeEnums.RiskModel,
        },
      });
      expect(distributedResource).toBeDefined();
      expect(distributedResource.orgId).toBe(testUser.currentOrg);

      // 3. 验证关联关系
      const groups = await entityManager.find(GroupEntity, {
        where: { modelId: result.modelId },
      });
      const groupMetricRelations = await entityManager.find(GroupMetricRelationEntity, {
        where: { groupId: In(groups.map((t) => t.groupId)) },
      });
      expect(groupMetricRelations.length).toBeGreaterThan(0);
      const metricDimensionRelations = await entityManager.find(MetricDimensionRelationEntity, {
        where: { metricsId: In(groupMetricRelations.map((t) => t.metricsId)) },
      });
      expect(metricDimensionRelations.length).toBeGreaterThan(0);
      expect(_.uniq(metricDimensionRelations.map((t) => t.metricsId)).length).toEqual(groupMetricRelations.map((t) => t.metricsId).length);
    });
  });

  describe('sf 贷前模型 方法测试', () => {
    it('应该成功创建sf贷前尽调模型并正确设置所有相关数据', async () => {
      // Given
      const modelName = '【尽调】(sf贷前)测试模型';
      const modelType = RiskModelTypeEnums.GeneralRiskModel;

      // When
      result = await riskModelICBCSZService.createICBCSZModel(testUser, modelName, testUser.currentOrg, modelType);

      // Then
      // 1. 验证基本属性
      expect(result).toBeDefined();
      expect(result.modelId).toBeGreaterThan(0);
      const savedModel = await entityManager.findOne(RiskModelEntity, {
        where: { modelId: result.modelId },
      });
      expect(savedModel).toBeDefined();
      expect(savedModel.orgId).toBe(testOrgId);
      expect(savedModel.status).toBe(DataStatusEnums.Developing);

      // 2. 验证分发资源记录
      const distributedResource = await entityManager.findOne(DistributedSystemResourceEntity, {
        where: {
          resourceId: result.modelId,
          resourceType: DistributedResourceTypeEnums.RiskModel,
        },
      });
      expect(distributedResource).toBeDefined();
      expect(distributedResource.orgId).toBe(testUser.currentOrg);

      // 3. 验证关联关系
      const groups = await entityManager.find(GroupEntity, {
        where: { modelId: result.modelId },
      });
      const groupMetricRelations = await entityManager.find(GroupMetricRelationEntity, {
        where: { groupId: In(groups.map((t) => t.groupId)) },
      });
      expect(groupMetricRelations.length).toBeGreaterThan(0);
      const metricDimensionRelations = await entityManager.find(MetricDimensionRelationEntity, {
        where: { metricsId: In(groupMetricRelations.map((t) => t.metricsId)) },
      });
      expect(metricDimensionRelations.length).toBeGreaterThan(0);
      expect(_.uniq(metricDimensionRelations.map((t) => t.metricsId)).length).toEqual(groupMetricRelations.map((t) => t.metricsId).length);
    });
  });
});
