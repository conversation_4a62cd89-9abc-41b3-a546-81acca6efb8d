import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { PlatformUser } from '../../../libs/model/common';
import { RiskModelService } from '../risk_model.service';
import * as Bluebird from 'bluebird';
import { createDimensionFields, createGroup, createMetric } from '../../test_utils_module/dimension.test.utils';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DimensionService } from '../../dimension/dimension.service';
import { DimensionFieldsService } from '../../dimension/dimension.fields.service';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { YearPeriodType } from '../../../libs/constants/recruitment.constants';
import {
  hasCertificationRevoked,
  HasCompanyCircularShareholder,
  hasEmployeeStockPlatform,
  IndustrialChainCoreCompanyContant,
  InternationPatentStatusConstant,
  IsHistoryPatentConstant,
  IsInstitutionalInvestorConstant,
  PatentStableConstant,
  PatentStatisticsConstant,
  PatentTypeConstant,
  RecruitmentStatisticsConstant,
  SourcesInvestInstiteRankConstant,
} from '../../../libs/constants/company.constants';
import { BaseLineDateSelect, RegisCapitalTrendMap, registrationRatioType } from '../../../libs/constants/risk.change.constants';
import { StrategyRoleEnums } from '../../../libs/enums/StrategyRoleEnums';

/**
 * 企业科创健康性尽调指标
 */
@Injectable()
export class RiskModelSTIHealthService {
  private logger: Logger = QccLogger.getLogger(RiskModelSTIHealthService.name);

  constructor(
    private readonly riskModelService: RiskModelService,
    private readonly dimensionService: DimensionService,
    private readonly dimensionFieldsService: DimensionFieldsService,
    @InjectRepository(RiskModelEntity) private readonly riskModelRepo: Repository<RiskModelEntity>,
  ) {}

  /**
   * 手动创建企业科创健康性模型
   * @param user 创建模型用户
   * @param modelName  模型名称
   * @param toOrgId  分发给的orgId
   * @param modelType
   * @returns
   */
  async createSTIIndicatorsModel(
    user: PlatformUser,
    modelName: string,
    toOrgId?: number,
    modelType: RiskModelTypeEnums = RiskModelTypeEnums.GeneralRiskModel,
  ): Promise<RiskModelEntity> {
    // 1. 创建风险模型
    const riskModel = await this.riskModelService.addRiskModel(
      {
        modelName,
        product: user.currentProduct,
        comment: modelName,
        status: DataStatusEnums.Developing,
        modelType,
      },
      user,
    );

    const manager = this.riskModelRepo.manager;

    // 2. 初始化维度，维度属性
    await createDimensionFields(manager, user);

    // 3. 创建分组
    const [g1, g2, g3, g4] = await Bluebird.all([
      createGroup(manager, user, riskModel.modelId, '企业发展性', 1),
      createGroup(manager, user, riskModel.modelId, '企业创新性', 2),
      createGroup(manager, user, riskModel.modelId, '企业稳定性', 3),
      createGroup(manager, user, riskModel.modelId, '关联方稳定性', 4),
    ]);

    // 4. 给分组内添加指标
    await Bluebird.all([
      createMetric(
        manager,
        user,
        g1.groupId,
        '本科以上招聘占比',
        'Xn=N期内招聘本科以上学历数量/N期内招聘总数量，X=(X1+X2+...+XN)/N; σ={[(X1-X)^2+(X2-X)^2+...(XN-X)^2]/N}^1/2; CV=σ/X ',
        1,
        [
          {
            order: 0,
            maxScore: 3,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '高X (≥80%)+ 低CV(＜=20%)',
                comment:
                  '命中 【1】n期平均值X >= 80 , 【2】n期变异系数CV n期标准差与平均值的比值 CV <= 20,  【3】时间范围 近三年， 【4】数据范围 有效， 【5】排序规则 publishtime, 【6】周期年(1期)',
                dimKey: DimensionTypeEnums.RecruitmentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
                    fieldValue: [1],
                    options: RecruitmentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'PublishTime' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [3],
                    options: [-1, 1, 2, 3, 4, 5],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [1, 2, 3],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 2.4,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '高X (≥80%)+ 高CV(>20%)',
                comment:
                  '命中 【1】n期平均值X >= 80 , 【2】n期变异系数CV n期标准差与平均值的比值 CV >= 20,  【3】时间范围 近三年， 【4】数据范围 有效， 【5】排序规则 publishtime, 【6】周期年(1期)',
                dimKey: DimensionTypeEnums.RecruitmentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
                    fieldValue: [1],
                    options: RecruitmentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'PublishTime' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [3],
                    options: [-1, 1, 2, 3, 4, 5],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [1, 2, 3],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 1.2,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '低X (<80%)+ 低CV(<=20%)',
                comment:
                  '命中 【1】n期平均值X < 80 , 【2】n期变异系数CV n期标准差与平均值的比值 CV <= 20,  【3】时间范围 近三年， 【4】数据范围 有效， 【5】排序规则 publishtime, 【6】周期年(1期)',
                dimKey: DimensionTypeEnums.RecruitmentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
                    fieldValue: [1],
                    options: RecruitmentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'PublishTime' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [3],
                    options: [-1, 1, 2, 3, 4, 5],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [1, 2, 3],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 0.6,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '低X (<80%)+ 高CV(>20%)',
                comment:
                  '命中 【1】n期平均值X < 80 , 【2】n期变异系数CV n期标准差与平均值的比值 CV > 20,  【3】时间范围 近三年， 【4】数据范围 有效， 【5】排序规则 PublishTime, 【6】周期年(1期)',
                dimKey: DimensionTypeEnums.RecruitmentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
                    fieldValue: [1],
                    options: RecruitmentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'PublishTime' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [3],
                    options: [-1, 1, 2, 3, 4, 5],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [1, 2, 3],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 0,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: 'X=0',
                comment: '命中 【1】n期平均值X = 0  【3】时间范围 近三年， 【4】数据范围 有效， 【5】排序规则 PublishTime, 【6】周期年(1期)',
                dimKey: DimensionTypeEnums.RecruitmentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
                    fieldValue: [1],
                    options: RecruitmentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'PublishTime' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [3],
                    options: [-1, 1, 2, 3, 4, 5],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [0],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [1, 2, 3],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isShowTip,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '提示' },
                      { value: 2, label: '不提示' },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
      ),
      createMetric(manager, user, g1.groupId, '近三年新增股权融资情况', '是否有投融资，若有，投资机构属性如何 ', 2, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: 'A类投资者，投资主体包含“中国PE/VC行业评选”榜单机构、清科榜单机构或市级以上国资机构',
              comment:
                '衡量企业在资本市场的认可度，命中 【1】时间范围 近三年 【2】数据范围 有效，【3】有融资 【4】排序规则 financedate 【5】有限合伙 【6】投资机构榜单来源  ',
              dimKey: DimensionTypeEnums.EquityFinancing,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'financedate', order: 'DESC', fieldSnapshot: '"FinanceDate"' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
                  fieldValue: [1],
                  options: IsInstitutionalInvestorConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sourcesInvestInstiteRank,
                  fieldValue: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
                  options: SourcesInvestInstiteRankConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 4,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: 'B类投资者，投资主体是具有产业链核心企业(上市公司、国央企、世界/中国500强企业，包含上述公司的关联公司)背景的投资机构',
              comment:
                '衡量企业在资本市场的认可度，命中 【1】时间范围 近三年 【2】数据范围 有效，【3】有融资 【4】排序规则 financedate 【5】上市公司、国央企、世界/中国500强企业，包含上述公司的关联公司  ',
              dimKey: DimensionTypeEnums.EquityFinancing,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'financedate', order: 'DESC', fieldSnapshot: '"FinanceDate"' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
                  fieldValue: [1],
                  options: IsInstitutionalInvestorConstant,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isIndustrialChainCoreCompany,
                  fieldValue: [1],
                  options: IndustrialChainCoreCompanyContant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Alert,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: 'C类投资者，其他投资机构',
              comment: '命中 【1】时间范围 近三年 【2】数据范围 有效，【3】有融资，【4】是合伙企业',
              dimKey: DimensionTypeEnums.EquityFinancing,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'financedate', order: 'DESC', fieldSnapshot: '"FinanceDate"' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
                  fieldValue: [1],
                  options: IsInstitutionalInvestorConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: 'C类投资者，无投资信息',
              comment: '命中 【1】时间范围 近三年 【2】数据范围 有效，【3】无融资',
              dimKey: DimensionTypeEnums.EquityFinancing,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'financedate', order: 'DESC', fieldSnapshot: '"FinanceDate"' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '发明专利占比', '近5年1期申请的发明专利数量/近5年1期申请的专利总数量*100%', 3, [
        {
          order: 0,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '≥80%',
              comment: '命中 【1】时间范围 近5年【3】专利类型【5】发明专利占比≥80%',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [1],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [80],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 8,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '[60%，80%)',
              comment: '命中 【1】时间范围 近5年 【3】专利类型 【5】发明专利占比[60%,80%)',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [1],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [60],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [80],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 6,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '[40%，60%)',
              comment: '命中 【1】时间范围 近5年【3】专利类型【5】发明专利占比[40%,60%)',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [1],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [40],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [60],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: 4,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '[20%，40%)',
              comment: '命中 【1】时间范围 近5年【3】专利类型【5】发明专利占比[20%,40%)',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [1],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [20],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [40],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 4,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '[10%，20%)',
              comment: '命中 【1】时间范围 近5年 【3】专利类型【5】发明专利占比[10%,20%)',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [1],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [10],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [20],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 5,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '<10%',
              comment: '命中 【1】时间范围 近5年 【3】专利类型【5】<10%',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [1],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [10],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(
        manager,
        user,
        g2.groupId,
        '发明专利平均占比',
        'Xn=当期申请的发明专利数量/当期申请的专利总数量*100% X=(X1+X2+...+XN)/N σ={[(X1-X)^2+(X2-X)^2+...(XN-X)^2]/N}^1/2 CV=σ/X X=0，则赋0分',
        4,
        [
          {
            order: 0,
            maxScore: 5,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '高X(≥80%) + 低CV(≤20%)',
                comment:
                  '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X >= 80 , 【6】n期变异系数CV n期标准差与平均值的比值 CV <= 20, ',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [2],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 4,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '高X(≥80%) + 高CV(＞20%)',
                comment:
                  '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X >= 80 , 【6】n期变异系数CV n期标准差与平均值的比值 CV > 20,',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [2],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 3,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '低X(＜80%) + 低CV(≤20%)',
                comment:
                  '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X < 80  【6】n期变异系数CV n期标准差与平均值的比值 CV <= 20, ',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [2],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 2,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '低X(＜80%) + 高CV(＞20%)',
                comment:
                  '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X < 80 , 【6】n期变异系数CV n期标准差与平均值的比值 CV > 20,',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [2],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 5,
            maxScore: 0,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: 'X=0',
                comment: '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X=0,',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [2],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [0],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isShowTip,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '提示' },
                      { value: 2, label: '不提示' },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
      ),

      createMetric(manager, user, g2.groupId, '发明专利授权率', '近5年申请的发明专利且评估日处于授权状态的发明专利数量/近5年申请的发明专利数量*100%', 5, [
        {
          order: 0,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '≥50%',
              comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利【4】一期【5】占比≥50%，【5】专利状态：授权',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [3],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 8,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '[30%，50%)',
              comment: '命中 【1】时间范围 近5年 【3】专利类型： 发明专利 【4】一期，【5】占比[30%,50%) ，【6】专利状态：授权',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [3],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [30],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 6,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '[20%，30%)',
              comment: '命中 【1】时间范围 近5年【3】专利类型： 发明专利 【4】一期，【5】占比[20%,30%)， 【6】专利状态：授权',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [3],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [20],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [30],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: 4,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '[10%，20%)',
              comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利 【4】一期，【5】占比[10%,20%)， 【6】专利状态：授权',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [3],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [10],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [20],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 4,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '(0%，10%)',
              comment: '命中 【1】时间范围 近5年 【3】专利类型，发明专利 【4】一期，【5】占比(0%,10%)，【6】专利状态：授权',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [3],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.leftRatio,
                  fieldValue: [0],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [10],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 5,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '0',
              comment: '命中 【1】时间范围 近5年 【3】专利类型:发明专利 【4】一期，【5】占比0， 【6】专利状态：授权',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [3],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.rightRatio,
                  fieldValue: [0],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(
        manager,
        user,
        g2.groupId,
        '发明专利平均授权率',
        'Xn=当期申请的发明专利且评估日处于授权状态的数量/当期申请的发明专利数量*100% X=(X1+X2+...+XN)/N σ={[(X1-X)^2+(X2-X)^2+...(XN-X)^2]/N}^1/2 CV=σ/X 如果X=0，则赋值0',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '高X(≥30%) + 低CV(≤100%)',
                comment:
                  '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X >= 30 , 【6】n期变异系数CV n期标准差与平均值的比值 CV < 100, 【7】专利状态：授权',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [4],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [30],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [100],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 8,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '高X(≥30%) + 高CV(＞100%)',
                comment:
                  '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X >= 30 , 【6】n期变异系数CV n期标准差与平均值的比值 CV > 100, 【7】专利状态：授权',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [4],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [30],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [100],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 6,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '低X(＜30%) + 低CV(≤100%)',
                comment:
                  '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X < 30  【6】n期变异系数CV n期标准差与平均值的比值 CV <= 100， 【7】专利状态：授权',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [4],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [30],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [100],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 4,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '低X(＜30%) + 高CV(＞100%)',
                comment:
                  '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X < 30 , 【6】n期变异系数CV n期标准差与平均值的比值 CV > 100， 【7】专利状态：授权',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [4],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [30],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cvXn,
                    fieldValue: [100],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 0,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: 'X=0',
                comment: '命中 【1】时间范围 近三年不含当期【3】专利类型 【4】二三四期, 【5】n期平均值X=0, 【7】专利状态：授权',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [4],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [4],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.yearPeriod,
                    fieldValue: [2, 3, 4],
                    options: YearPeriodType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.avgXn,
                    fieldValue: [0],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isShowTip,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '提示' },
                      { value: 2, label: '不提示' },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
      ),

      createMetric(
        manager,
        user,
        g2.groupId,
        '发明专利发明人集中度',
        'Xn=近5年1期发明专利发明人包含董、监、高、法及自然人股东的发明专利数量/近5年1期发明专利总数量*100% Xn=N(分母为0)，则赋0分',
        7,
        [
          {
            order: 0,
            maxScore: 2,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '<20%',
                comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利【4】一期【5】占比≥50%， 【6】发明人是否包含： 董、监、高、法及自然人股东',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [5],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 1.6,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '[20%，40%)',
                comment: '命中 【1】时间范围 近5年 【3】专利类型： 发明专利 【4】一期，【5】占比[20%,40%)【6】发明人是否包含： 董、监、高、法及自然人股东',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [5],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [40],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 1.2,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '[40%，60%)',
                comment: '命中 【1】时间范围 近5年【3】专利类型： 发明专利 【4】一期，【5】占比[40%,60%)【6】发明人是否包含： 董、监、高、法及自然人股东',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [5],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [40],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [60],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 0.8,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '[60%，80%)',
                comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利 【4】一期，【5】占比[60%,80%)【6】发明人是否包含： 董、监、高、法及自然人股东',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [5],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [60],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 0.4,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '[80%，100%]',
                comment: '命中 【1】时间范围 近5年 【3】专利类型，发明专利 【4】一期，【5】占比[80%,100%]【6】发明人是否包含： 董、监、高、法及自然人股东',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [5],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [100],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 5,
            maxScore: 0,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '0',
                comment: '命中 【1】时间范围 近5年 【3】专利类型:发明专利 【4】一期，【5】占比0 【6】发明人是否包含： 董、监、高、法及自然人股东',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [5],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [0],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isShowTip,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '提示' },
                      { value: 2, label: '不提示' },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
      ),

      createMetric(
        manager,
        user,
        g2.groupId,
        '以转让方式获取的发明专利占比',
        'Xn=近5年1期转让获得的发明专利数量/近5年1期发明专利总数量 *100% Xn=N(分母为0)，则赋0分 发明专利=发明公布+发明授权',
        8,
        [
          {
            order: 0,
            maxScore: 3,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '<10%',
                comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利【4】一期【5】占比<10%， 【6】是否以转让方式获得',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [6],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [10],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 2.4,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '[10%，30%)',
                comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利【4】一期【5】占比[10%,30%)， 【6】是否以转让方式获得',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [6],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [10],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [30],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 1.8,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '[30%，50)',
                comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利【4】一期【5】占比[30%,50)， 【6】是否以转让方式获得',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [6],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [30],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 1.2,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '[50%，70%)',
                comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利【4】一期【5】占比[50%,70%)， 【6】是否以转让方式获得',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [6],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.rightRatio,
                    fieldValue: [70],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 0.6,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '≥70%',
                comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利【4】一期【5】占比≥70%， 【6】是否以转让方式获得',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [6],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [70],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 5,
            maxScore: 0,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '0',
                comment: '命中 【1】时间范围 近5年【3】专利类型：发明专利【4】一期【5】占比0， 【6】是否以转让方式获得',
                dimKey: DimensionTypeEnums.PatentAnalysis,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatistics,
                    fieldValue: [6],
                    options: PatentStatisticsConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.naturalCycle,
                    fieldValue: [6],
                    options: [-1, 1, 2, 3, 4, 5, 6],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.leftRatio,
                    fieldValue: [0],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sortField,
                    fieldValue: [
                      {
                        field: 'applicationdate',
                        order: 'DESC',
                        fieldSnapshot: 'ApplicatioDate',
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isShowTip,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '提示' },
                      { value: 2, label: '不提示' },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
      ),

      createMetric(manager, user, g2.groupId, '发明专利申请稳定性', '发明专利申请稳定性', 9, [
        {
          order: 0,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '最近3年1期，任意3个年度有专利申请',
              comment: '命中 【1】时间范围 近3年【2】一期,二期，三期，四期 【3】稳定性 ',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [7],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.yearPeriod,
                  fieldValue: [1, 2, 3, 4],
                  options: YearPeriodType,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentStable,
                  fieldValue: [3],
                  options: PatentStableConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 8,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '最近3年1期，任意3个年度有专利申请',
              comment: '命中 【1】时间范围 近3年【2】一期,二期，三期，四期, 【3】稳定性',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [7],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.yearPeriod,
                  fieldValue: [1, 2, 3, 4],
                  options: YearPeriodType,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentStable,
                  fieldValue: [2],
                  options: PatentStableConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 4,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '最近3年1期，任意1个年度有专利申请',
              comment: '命中 【1】时间范围 近3年【2】一期,二期，三期，四期, 【3】稳定性',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [7],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.yearPeriod,
                  fieldValue: [1, 2, 3, 4],
                  options: YearPeriodType,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentStable,
                  fieldValue: [1],
                  options: PatentStableConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '最近3年1期，无专利申请',
              comment: '命中 【1】时间范围 近3年【2】一期,二期，三期，四期, 【3】稳定性',
              dimKey: DimensionTypeEnums.PatentAnalysis,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.patentStatistics,
                  fieldValue: [7],
                  options: PatentStatisticsConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.yearPeriod,
                  fieldValue: [1, 2, 3, 4],
                  options: YearPeriodType,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentStable,
                  fieldValue: [0],
                  options: PatentStableConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g2.groupId, '发明专利流出', '最近1年1期，是否有发明专利流出企业 若近5年无发明专利申请，则赋0分', 10, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          operation: 'must',
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              dimStrategyName: '近5年有发明专利申请',
              comment: '命中 【1】当前专利 【2】发明专利',
              dimKey: DimensionTypeEnums.PatentInfo,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '最近1年1期，无发明专利流出企业',
              comment: '命中 【1】历史专利，【2】近2年，【3】1期，【4】发明专利',
              dimKey: DimensionTypeEnums.PatentInfo,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [2],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isHistoryPatent,
                  fieldValue: [1],
                  options: IsHistoryPatentConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '近5年无发明专利申请',
              comment: '命中 【1】当前专利 【2】发明专利',
              dimKey: DimensionTypeEnums.PatentInfo,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: -5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          operation: 'must',
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              dimStrategyName: '近5年有发明专利申请',
              comment: '命中 【1】当前专利 【2】发明专利',
              dimKey: DimensionTypeEnums.PatentInfo,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '最近1年1期，有发明专利流出企业',
              comment: '命中 【1】历史专利，【2】近2年，【3】1期，【4】发明专利',
              dimKey: DimensionTypeEnums.PatentInfo,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [2],
                  options: [-1, 1, 2, 3, 4, 5, 6],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isHistoryPatent,
                  fieldValue: [1],
                  options: IsHistoryPatentConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.patentType,
                  fieldValue: ['1', '2'],
                  options: PatentTypeConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g2.groupId, '有效PCT国际专利', '是否存在有效的PCT国际专利', 11, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '存在有效的PCT国际专利',
              comment: '命中 【1】存在',
              dimKey: DimensionTypeEnums.InternationPatent,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.internationPatentStatus,
                  fieldValue: ['ZT005002', 'ZT006002'],
                  options: InternationPatentStatusConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicatioDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '不存在有效的PCT国际专利',
              comment: '命中 【1】不存在',
              dimKey: DimensionTypeEnums.InternationPatent,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.internationPatentStatus,
                  fieldValue: ['ZT005002', 'ZT006002'],
                  options: InternationPatentStatusConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [
                    {
                      field: 'applicationdate',
                      order: 'DESC',
                      fieldSnapshot: 'ApplicationDate',
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '省级以上荣誉个数', '省级以上荣誉个数', 12, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '省级以上荣誉个数>=5',
              comment: '命中 省级以上荣誉个数>=5',
              dimKey: DimensionTypeEnums.ProvincialHonor,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  accessScope: 2,
                  fieldValue: [5],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 4,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '省级以上荣誉个数=4',
              comment: '命中 省级以上荣誉个数=4',
              dimKey: DimensionTypeEnums.ProvincialHonor,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  accessScope: 2,
                  fieldValue: [4],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '省级以上荣誉个数=3',
              comment: '命中 省级以上荣誉个数=3',
              dimKey: DimensionTypeEnums.ProvincialHonor,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  accessScope: 2,
                  fieldValue: [3],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '省级以上荣誉个数=2',
              comment: '命中 省级以上荣誉个数=2',
              dimKey: DimensionTypeEnums.ProvincialHonor,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  accessScope: 2,
                  fieldValue: [2],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 4,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '省级以上荣誉个数=1',
              comment: '命中 省级以上荣誉个数=1',
              dimKey: DimensionTypeEnums.ProvincialHonor,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  accessScope: 2,
                  fieldValue: [1],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 5,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '省级以上荣誉个数=0',
              comment: '命中 省级以上荣誉个数=0',
              dimKey: DimensionTypeEnums.ProvincialHonor,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  accessScope: 2,
                  fieldValue: [0],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '是否存在省级以上资质被取消', '是否存在省级以上资质被取消', 13, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '不存在省级以上资质被取消',
              comment: '命中 不存在省级以上资质被取消',
              dimKey: DimensionTypeEnums.ProvincialHonor,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.HasCertificationRevoked,
                  accessScope: 2,
                  options: hasCertificationRevoked,
                  fieldValue: [0],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: -5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '存在省级以上资质被取消',
              comment: '命中 存在省级以上资质被取消',
              dimKey: DimensionTypeEnums.ProvincialHonor,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.HasCertificationRevoked,
                  accessScope: 2,
                  options: hasCertificationRevoked,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '董监高法变动频率', '董监高法变动频率', 1, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近3年董监高法变动次数<=1',
              comment: '命中 董监高法变动频率',
              dimKey: DimensionTypeEnums.MainMembersChangeFrequency,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.MainMembersChangeFrequency,
                  accessScope: 2,
                  fieldValue: [1],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近3年董监高法变动次数=2',
              comment: '命中 董监高法变动频率',
              dimKey: DimensionTypeEnums.MainMembersChangeFrequency,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.MainMembersChangeFrequency,
                  accessScope: 2,
                  fieldValue: [2],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近3年董监高法变动次数在3-5次',
              comment: '命中 董监高法变动频率',
              dimKey: DimensionTypeEnums.MainMembersChangeFrequency,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.MainMembersChangeFrequency,
                  accessScope: 2,
                  fieldValue: [3, 5],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.Between,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: -2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近3年董监高法变动次数>=5',
              comment: '命中 董监高法变动频率',
              dimKey: DimensionTypeEnums.MainMembersChangeFrequency,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.MainMembersChangeFrequency,
                  accessScope: 2,
                  fieldValue: [5],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [4],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '是否有员工持股平台', '是否有员工持股平台', 2, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '有员工持股平台',
              comment: '命中 有员工持股平台',
              dimKey: DimensionTypeEnums.EmployeeStockPlatform,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.HasEmployeeStockPlatform,
                  accessScope: 2,
                  options: hasEmployeeStockPlatform,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '无员工持股平台',
              comment: '命中 无员工持股平台',
              dimKey: DimensionTypeEnums.EmployeeStockPlatform,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.HasEmployeeStockPlatform,
                  accessScope: 2,
                  options: hasEmployeeStockPlatform,
                  fieldValue: [0],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '股权变更频率', '股权变更频率', 3, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '股权变更频率<3',
              comment: '命中 股权变更频率<3',
              dimKey: DimensionTypeEnums.MainMembersChangeFrequency,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.equityChangeFrequency,
                  accessScope: 2,
                  fieldValue: [3],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '股权变更频率>=3',
              comment: '命中 股权变更频率>=3',
              dimKey: DimensionTypeEnums.MainMembersChangeFrequency,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.equityChangeFrequency,
                  accessScope: 2,
                  fieldValue: [3],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [6],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '股权结构异常', '股权结构异常', 4, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '股权结构正常',
              comment: '命中 股权结构正常',
              dimKey: DimensionTypeEnums.EquityStructureAbnormal,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.primaryShareholderAbnormality,
                  accessScope: 2,
                  options: [{}],
                  fieldValue: [],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.secondaryShareholderAbnormality,
                  accessScope: 2,
                  options: [{}],
                  fieldValue: [],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.CircularShareholdingBetweenRelatedParties,
                  accessScope: 2,
                  options: HasCompanyCircularShareholder,
                  fieldValue: [0],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: -10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '股权结构异常',
              comment: '命中 股权结构异常',
              dimKey: DimensionTypeEnums.EquityStructureAbnormal,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.primaryShareholderAbnormality,
                  accessScope: 2,
                  options: [{}],
                  fieldValue: [],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.secondaryShareholderAbnormality,
                  accessScope: 2,
                  options: [{}],
                  fieldValue: [],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.CircularShareholdingBetweenRelatedParties,
                  accessScope: 2,
                  options: HasCompanyCircularShareholder,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '减资', '减资', 5, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '近2年1期累计减资比例0%-5%',
              comment: '近2年1期累计减资比例，最高点-评估时',
              dimKey: DimensionTypeEnums.MainInfoUpdateCapitalChange,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [37],
                  options: [{ value: 37, label: '注册资本' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                  fieldValue: [
                    {
                      valuePeriodTrend: 1,
                      valuePeriodThreShold: [[0, 5]],
                      valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
                      valuePeriodBaseLine: 1,
                    },
                  ],
                  options: [
                    {
                      valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                      valuePeriodThreShold: {
                        label: '占比',
                        value: { unit: '%', min: 0, max: 100 },
                      },
                      valuePeriodThreSholdCompareType: {
                        label: '占比比较',
                        value: DimensionFieldCompareTypeEnums.ContainsAny,
                      },
                      valuePeriodBaseLine: {
                        label: '时间基准',
                        value: BaseLineDateSelect,
                      },
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '近2年1期累计减资比例>=5% && < 25%',
              comment: '近2年1期累计减资比例，最高点-评估时',
              dimKey: DimensionTypeEnums.MainInfoUpdateCapitalChange,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [37],
                  options: [{ value: 37, label: '注册资本' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                  fieldValue: [
                    {
                      valuePeriodTrend: 1,
                      valuePeriodThreShold: [[5, 25]],
                      valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
                      valuePeriodBaseLine: 1,
                    },
                  ],
                  options: [
                    {
                      valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                      valuePeriodThreShold: {
                        label: '占比',
                        value: { unit: '%', min: 0, max: 100 },
                      },
                      valuePeriodThreSholdCompareType: {
                        label: '占比比较',
                        value: DimensionFieldCompareTypeEnums.ContainsAny,
                      },
                      valuePeriodBaseLine: {
                        label: '时间基准',
                        value: BaseLineDateSelect,
                      },
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: -3,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '近2年1期累计减资比例>=25% && <50%',
              comment: '近2年1期累计减资比例，最高点-评估时',
              dimKey: DimensionTypeEnums.MainInfoUpdateCapitalChange,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [37],
                  options: [{ value: 37, label: '注册资本' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                  fieldValue: [
                    {
                      valuePeriodTrend: 1,
                      valuePeriodThreShold: [[25, 50]],
                      valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
                      valuePeriodBaseLine: 1,
                    },
                  ],
                  options: [
                    {
                      valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                      valuePeriodThreShold: {
                        label: '占比',
                        value: { unit: '%', min: 0, max: 100 },
                      },
                      valuePeriodThreSholdCompareType: {
                        label: '占比比较',
                        value: DimensionFieldCompareTypeEnums.ContainsAny,
                      },
                      valuePeriodBaseLine: {
                        label: '时间基准',
                        value: BaseLineDateSelect,
                      },
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: -4.5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '近2年1期累计减资比例>=50% && < 75%',
              comment: '近2年1期累计减资比例，最高点-评估时',
              dimKey: DimensionTypeEnums.MainInfoUpdateCapitalChange,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [37],
                  options: [{ value: 37, label: '注册资本' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                  fieldValue: [
                    {
                      valuePeriodTrend: 1,
                      valuePeriodThreShold: [[50, 75]],
                      valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
                      valuePeriodBaseLine: 1,
                    },
                  ],
                  options: [
                    {
                      valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                      valuePeriodThreShold: {
                        label: '占比',
                        value: { unit: '%', min: 0, max: 100 },
                      },
                      valuePeriodThreSholdCompareType: {
                        label: '占比比较',
                        value: DimensionFieldCompareTypeEnums.ContainsAny,
                      },
                      valuePeriodBaseLine: {
                        label: '时间基准',
                        value: BaseLineDateSelect,
                      },
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 4,
          maxScore: -6,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '近2年1期累计减资比例>=75%',
              comment: '近2年1期累计减资比例，最高点-评估时',
              dimKey: DimensionTypeEnums.MainInfoUpdateCapitalChange,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [37],
                  options: [{ value: 37, label: '注册资本' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                  fieldValue: [
                    {
                      valuePeriodTrend: 1,
                      valuePeriodThreShold: [[75]],
                      valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
                      valuePeriodBaseLine: 1,
                    },
                  ],
                  options: [
                    {
                      valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                      valuePeriodThreShold: {
                        label: '占比',
                        value: { unit: '%', min: 0, max: 100 },
                      },
                      valuePeriodThreSholdCompareType: {
                        label: '占比比较',
                        value: DimensionFieldCompareTypeEnums.ContainsAny,
                      },
                      valuePeriodBaseLine: {
                        label: '时间基准',
                        value: BaseLineDateSelect,
                      },
                    },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '注册资本实缴比例', '注册资本实缴比例', 6, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '实缴资本/注册资本 = 100%',
              comment: '命中 实缴资本/注册资本 = 100%',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.registrationRatio,
                  accessScope: 2,
                  options: registrationRatioType,
                  fieldValue: [[100]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 1.6,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '实缴资本/注册资本 = 80%-100%',
              comment: '命中 实缴资本/注册资本 = 80%-100%',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.registrationRatio,
                  accessScope: 2,
                  options: registrationRatioType,
                  fieldValue: [[80, 100]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 1.2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '实缴资本/注册资本 = 60%-80%',
              comment: '命中 实缴资本/注册资本 = 60%-80%',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.registrationRatio,
                  accessScope: 2,
                  options: registrationRatioType,
                  fieldValue: [[60, 80]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: 0.8,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '实缴资本/注册资本 = 40%-60%',
              comment: '命中 实缴资本/注册资本 = 40%-60%',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.registrationRatio,
                  accessScope: 2,
                  options: registrationRatioType,
                  fieldValue: [[40, 60]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 4,
          maxScore: 0.4,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '实缴资本/注册资本 = 0%-40%',
              comment: '命中 实缴资本/注册资本 = 0%-40%',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.registrationRatio,
                  accessScope: 2,
                  options: registrationRatioType,
                  fieldValue: [[0, 40]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 5,
          maxScore: 0,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '实缴资本/注册资本 = 0%',
              comment: '命中 实缴资本/注册资本 = 0%',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.registrationRatio,
                  accessScope: 2,
                  options: registrationRatioType,
                  fieldValue: [[0, 0]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '是否被列入经营异常名录', '是否被列入经营异常名录', 1, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '未被列入经营异常名录',
              comment: '命中 未被列入经营异常名录',
              dimKey: DimensionTypeEnums.BusinessAbnormal3,
              fields: [
                {
                  // 经营异常类型
                  fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
                  fieldValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isShowTip,
                  fieldValue: [1],
                  options: [
                    { value: 1, label: '提示' },
                    { value: 2, label: '不提示' },
                  ],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: -2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '被列入经营异常名录',
              comment: '命中 被列入经营异常名录',
              dimKey: DimensionTypeEnums.BusinessAbnormal3,
              fields: [
                {
                  // 经营异常类型
                  fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
                  fieldValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '关联方集中注册或注销', '关联方集中注册或注销', 2, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '关联方企业集中注册或注销次数<3',
              comment: '命中 关联方集中注册或注销',
              dimKey: DimensionTypeEnums.RelatedCompanyMassRegistrationCancellation,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  accessScope: 2,
                  fieldValue: [3],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: -3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '关联方企业集中注册或注销次数>=3',
              comment: '命中 关联方集中注册或注销',
              dimKey: DimensionTypeEnums.RelatedCompanyMassRegistrationCancellation,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  accessScope: 2,
                  fieldValue: [3],
                  options: [{ unit: '个', min: 1, max: 50 }],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.naturalCycle,
                  fieldValue: [3],
                  options: [-1, 1, 2, 3, 4, 5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),
    ]);
    // 5. 分发模型给租户
    if (toOrgId) {
      await this.riskModelService.distributeRiskModel(
        {
          riskModelId: riskModel.modelId,
          orgId: toOrgId,
        },
        user,
      );
    }
    return riskModel;
  }
}
