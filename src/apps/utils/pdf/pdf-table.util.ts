import { get, intersection, isArray, isNil, isObject, isString, reverse, union, uniq } from 'lodash';
import { diffChars } from 'diff';
import * as moment from 'moment';
import Big from 'big.js';

type NumberToHumanOptions = {
  separator: string;
  precision: number;
};

const STRONG_LEVEL_RELATION = [
  'biddingcompanyrelationship', // 直接关系
  'BiddingCompanyRelationship', // 直接关系
  'legal',
  'employ',
  'invest',
  'hislegal',
  'hisemploy',
  'hisinvest',
  'branch',
  'actualcontroller',
  'guarantor',
  'equitypledge',
  'chattelmortgage',
  'controlrelation',
  '法定代表人',
  '董监高',
  '股东',
  '历史股东',
  '持股/投资关联',
  '历史法定代表人',
  '历史董监高',
  '历史持股/投资关联',
  '分支机构',
  '控制关系',
  '实际控制人',
  '相互担保关联',
  '股权出质关联',
  '动产抵押关联',
];
const NORMAL_LEVEL_RELATION = [
  'contactnumber',
  'mail',
  'address',
  'upanddownrelation',
  'bidcollusive',
  'website',
  '相同电话号码',
  '相同邮箱',
  '相同经营地址',
  '上下游关联',
  '围串标关联',
  '客户',
  '供应商',
  '相同域名信息',
];
const WEAK_LEVEL_RELATION = [
  'patent',
  'intpatent',
  'softwarecopyright',
  'case',
  'samenameemployee',
  '相同专利信息',
  '相同国际专利信息',
  '相同软件著作权',
  '相同司法案件',
  '疑似同名主要人员',
];

/**
 * 根据风险等级获取标签样式（强弱关系）
 */
export const getLevelStyle = (level: number) => {
  const STYLE_MAP = {
    0: {
      background: '#C4F5E0',
    },
    1: {
      background: '#fec',
    },
    2: {
      background: '#fcc',
    },
  };
  const style = STYLE_MAP[level] || {};
  const styleObject = {
    display: 'inline-block',
    height: '20px',
    borderRadius: '2px',
    'line-height': '18px',
    color: ' #666',
    padding: '0 4px',
    ...style,
  };
  const styleText = `display: ${styleObject.display}; background: ${styleObject.background}; color: ${styleObject.color}; border-radius: ${styleObject.borderRadius}; padding: ${styleObject.padding}`;
  return styleText;
};

/**
 * 获取关系类型 风险强弱等级 样式, 图谱的关系可能出现多种
 * @param dimensionLevel
 */
export const getRelationTag = (roleTypes: any[] | string = []) => {
  const roleData = isArray(roleTypes) ? roleTypes.filter((item) => item).map((d) => d.toLowerCase()) : [roleTypes.toLowerCase()];
  let level = 3;
  let label = '';
  if (intersection(STRONG_LEVEL_RELATION, roleData).length) {
    level = 2;
    label = '强关系';
  } else if (intersection(NORMAL_LEVEL_RELATION, roleData).length) {
    level = 1;
    label = '中等关系';
  } else if (intersection(WEAK_LEVEL_RELATION, roleData).length) {
    level = 0;
    label = '弱关系';
  } else {
    level = 3;
    label = '';
  }

  let result = '';

  if (label) {
    const styleText = getLevelStyle(level);
    result = `<span style="margin-left: 6px; ${styleText}">${label}</span>`;
  }

  return result;
};

export function isEdge(node: Record<string, any>) {
  return node?.startid !== undefined && node?.endid !== undefined && node?.direction !== undefined;
}

export function createEdge(item) {
  const DIRECTION_MAP = {
    '0': '-',
    '-1': '←',
    '1': '→',
  };

  const direction = DIRECTION_MAP[item.direction];
  const isPlainArrow = item.type === '0' && !item.role;
  const isLeftArrow = item.direction === -1;
  const isRightArrow = item.direction === 1;
  const isDash = item.direction === 0;

  // 纯箭头
  if (isPlainArrow) {
    return `<span> ${direction} </span>`;
  }

  const output = item.role ? `（${item.role}）` : '';
  // 带内容的箭头
  if (isLeftArrow) {
    return `<span> ←${output}- </span>`;
  }
  if (isRightArrow) {
    return `<span> -${output}→ </span>`;
  }
  if (isDash) {
    return `<span> -${output}- </span>`;
  }
  return '';
}

export function getEntities(value) {
  const entities: Array<string | number | { KeyNo: string; Name: string; Job?: string }> = Array.isArray(value) ? value : [value];
  const result = entities.map((entity) => {
    let name: string | number = '';
    if (typeof entity === 'string' || typeof entity === 'number') {
      name = entity;
    } else if (entity.Name) {
      name = entity.Name;
    }
    if (typeof entity === 'object' && entity.Job) {
      const tags =
        entity.Job?.split(',')
          .map((t) => `<span class="status-tag golden">${t}</span>`)
          .join('') ?? '';
      name = `<span>${name}</span>`;
      if (tags) {
        name += `<div>${tags}</div>`;
      }
    }
    return name;
  });
  return result.every((name) => !!name) ? result.join(', ') : '';
}

/**
 * 接收一个数字，返回一个带千分位的字符串
 */
export function numberToHuman(number: number, options: Partial<NumberToHumanOptions> = {}) {
  if (isNil(number)) {
    return '';
  }
  const { separator = ',', precision = 0 } = options;
  const [integer, decimal] = number.toFixed(precision).split('.');
  const integerLength = integer.length;
  const integerPart = integer
    .split('')
    .map((digit, index) => {
      if (index !== 0 && (integerLength - index) % 3 === 0) {
        return separator + digit;
      }
      return digit;
    })
    .join('');
  return decimal ? `${integerPart}.${decimal}` : integerPart;
}

export function dateParser(rowData, dataIndex, options = {}) {
  const settings = {
    format: 'YYYY-MM-DD',
    pattern: undefined,
    ...options,
  };
  const d = rowData[dataIndex];
  // 有效数据: '20180803'
  // 无效数据: 0
  if (String(d).length < 4) {
    return '';
  }
  const m = moment(d, settings.pattern);
  return m.isValid() ? m.format(settings.format) : '';
}

export const dateTrans = (date, needMultiple = true) => {
  if (date && date !== '-') {
    date = needMultiple ? date * 1000 : date;
    return moment(date).format('YYYY-MM-DD');
  }
  return '-';
};
/**
 * 货币解析
 */
export function moneyParser(rowData, dataIndex, options = { reduce: 1, precision: 2 }) {
  const defaults = {
    reduce: 1,
  };
  // const value = rowData[dataIndex];
  // return value ? value / 10000 : 0;
  return numberToHuman(rowData[dataIndex] / (options.reduce || defaults.reduce), {
    precision: options.precision || 0,
  });
}

/**
 * 货币(带单位)解析
 */
export function moneyWithUnitParser(rowData, dataIndex, options = { reduce: 1, precision: 2 }) {
  const val = rowData[dataIndex];
  if (isNil(val)) {
    return '-';
  }
  const [match] = val.toString().match(/^\d+/) || [];
  if (!match) {
    return val;
  }
  const defaults = {
    reduce: 1,
  };
  const human = numberToHuman(Number(match) / (options.reduce || defaults.reduce), {
    precision: options.precision || 0,
  });
  return val.toString().replace(match, human);
}

/**
 * 企业、人员列表解析
 * @param entities
 */
export function entitiesParser(rowData, dataIndex) {
  const entity = get(rowData, dataIndex);
  if (!entity) {
    return '';
  }
  return getEntities(entity);
}

/**
 * 合并省市区
 * @param area
 */
export const areaParse = (area = {} as { ProvinceName: string; CityName: string; CountyName: string }): string => {
  const { ProvinceName, CityName, CountyName } = area;
  const res: string[] = [];
  if (ProvinceName) {
    res.push(ProvinceName);
  }
  if (CityName && ProvinceName !== CityName) {
    res.push(CityName);
  }
  if (CountyName) {
    res.push(CountyName);
  }
  return res.join('');
};

/**
 * 状态标签
 */
export function statusParser(rowData, dataIndex, options = {}) {
  const item = get(rowData, dataIndex);
  if (!item) {
    return '-';
  }
  let type = 'success';
  let statusText = item;

  switch (statusText) {
    case '存续（在营、开业、在册）':
      statusText = '存续';
      break;
    case '注销':
    case '吊销':
    case '停业':
    case '撤销':
    case '清算':
    case '无效':
    case '责令关闭':
    case '不合格': // ExecuteStatus
      type = 'danger';
      break;
    case '筹建':
    case '迁入':
    case '迁出':
    case '歇业':
      type = 'warning';
      break;

    default:
      break;
  }
  return `<div class="status-tag ${type}">${statusText}</div>`;
}

/**
 * 专利法律状态标签颜色处理
 */
export const hanlePatentStatusColorParser = (record) => {
  const { Status, LegalStatusDesc } = record;
  let tagClass = 'default';

  const blueList = ['ZT001', 'ZT001001', 'ZT001002', 'ZT001003', 'ZT005', 'ZT005001', 'ZT005002'];
  const greenList = ['ZT002', 'ZT002001', 'ZT002002', 'ZT002003', 'ZT002004', 'ZT002005', 'ZT002006'];
  const redList = [
    'ZT003',
    'ZT003001',
    'ZT003002',
    'ZT003003',
    'ZT003004',
    'ZT003005',
    'ZT003006',
    'ZT003007',
    'ZT003008',
    'ZT003009',
    'ZT003010',
    'ZT003011',
    'ZT006',
    'ZT006001',
    'ZT006002',
  ];
  if (greenList.includes(Status)) {
    tagClass = 'success';
  } else if (redList.includes(Status)) {
    tagClass = 'danger';
  } else if (blueList.includes(Status)) {
    tagClass = 'primary';
  }
  return `<div class="status-tag ${tagClass}">${LegalStatusDesc}</div>`;
};

/**
 * 风险等级
 */
export function riskLevelParser(rowData) {
  const RISK_LEVEL_MAP = {
    1: {
      type: 'warning',
      label: '关注风险',
    },
    2: {
      type: 'danger',
      label: '警示风险',
    },
  };
  const level = rowData.level;
  if (level === undefined || !RISK_LEVEL_MAP[level]) {
    return '-';
  }
  const schema = RISK_LEVEL_MAP[level];
  return `<div class="risk-level-tag ${schema.type}">${schema.label}</div>`;
}

/**
 * 接收两段文本，输出比对结果，使用 <del> 标签包裹变更后被删除部分，使用 <em> 标签包裹变更后新增的部分
 */
export function diffParser(rowData, dataIndex, options: { before: string; after: string }) {
  if (!options.before || !options.after) {
    return '';
  }
  const before = get(rowData, options.before, '');
  const after = get(rowData, options.after, '');

  const output: string[] = [];

  const diffResult = diffChars(after, before);
  diffResult.forEach((part) => {
    let node = '';
    if (part.added) {
      node = `<em>${part.value}</em>`;
    } else if (part.removed) {
      node = `<del>${part.value}</del>`;
    } else {
      node = `<span>${part.value}</span>`;
    }
    output.push(node);
  });
  const html = output.join('');
  return `<div class="diff-result">${html}</div>`;
}

/**
 * 司法拍卖信息
 */
export function judicialAuctionParser(item, dataIndex, options = {}) {
  const title = item.name;
  let node = '';
  if (title) {
    node = `<div class="judicial-auction">${title}</div>`;
  }
  if (item.Caseno) {
    node += `<div style="color: #808080;">${item.Caseno}</div>`;
  }
  return node;
}

interface OptionsObject {
  left: string;
  right: string;
  middle: string;
  leftInfo: string;
  rightInfo: string;
}

/**
 * 关联路径
 */
export function pathParser(rowData, dataIndex, options: OptionsObject) {
  const left = get(rowData, options.left, '');
  const right = get(rowData, options.right, '');
  const middle = get(rowData, options.middle, '');
  const leftInfo = get(rowData, options.leftInfo, '');
  const rightInfo = get(rowData, options.rightInfo, '');

  const leftNode = left ? `<span>${left}</span>` : '';
  const rightNode = right ? `<span>${right}</span>` : '';
  const middleNode = middle ? `<span>${middle}</span>` : '';

  const createLine = (content: string, direction: 'left' | 'right') => {
    let output = content;
    // 特殊字段处理
    if (['stockpercent'].indexOf(options[`${direction}Info`]) > -1) {
      output = `${content}%`;
    }
    output = content ? `（${output}）` : '';

    switch (direction) {
      case 'left':
        return `<span>←${output}</span>`;
      case 'right':
        return `<span>${output}→</span>`;
      default:
        return '';
    }
  };

  return `<div>
    ${leftNode}
    ${leftNode ? createLine(leftInfo, 'left') : ''}
    ${middleNode}
    ${rightNode ? createLine(rightInfo, 'right') : ''}
    ${rightNode}
  </div>`;
}

/**
 * 黑名单有效期
 */
export function blacklistParser(rowData, dataIndex, options = {}) {
  const item = rowData[dataIndex];
  if (item === undefined || item === null) {
    return '';
  }
  const BLACKLIST_DURATION_MAP = {
    '0': '3个月',
    '1': '6个月',
    '2': '1年',
    '3': '2年',
    '4': '3年',
    '5': '5年',
    '-1': '不限',
  };
  return BLACKLIST_DURATION_MAP[item];
}

/**
 * 文本模板插值替换
 */
export function templateParser(rowData, dataIndex, options: { template: string }) {
  const item = rowData[dataIndex];
  if (item === undefined || item === null) {
    return '';
  }
  return options.template.replace(/\{value\}/g, item);
}

/**
 * from - to 日期范围
 */
export function rangeParser(rowData, dataIndex, options: { from: string; to: string; pattern?: string; type: string; separator: string }) {
  const PLACEHOLDER = '-';
  const SEPARATOR = ' 至 ';
  let start = get(rowData, options.from, '');
  let end = get(rowData, options.to, '');

  if (options.type === 'date') {
    start = dateParser(rowData, options.from, { pattern: options.pattern }) || PLACEHOLDER;
    end = dateParser(rowData, options.to, { pattern: options.pattern }) || PLACEHOLDER;
  }

  const range = [start, end];
  if (range.some((t) => t !== PLACEHOLDER)) {
    return range.join(options.separator || SEPARATOR);
  }
  return PLACEHOLDER;
}

export function jsonParser(rowData, dataIndex, options: { parser: string }) {
  const item = JSON.parse(get(rowData, dataIndex, '{}'));
  if (options.parser === 'entities') {
    return getEntities(item);
  }
  return '-';
}

export const renderCompanyNameRelated = (item) => {
  const elements = [`<span>${item.companyNameRelated}</span>`];
  if (item.history && item.role) {
    elements.push(`<span class="status-tag default">${item.role}</span>`);
  }
  return elements.join('');
};

// 招标排查: 关联黑名单企业名称
export const relatedCompanyNameWithHistoryRole = (item, options: { key: string }) => {
  const companyName = item.relatedCompanyName || item.companyNameRelated;
  const elements = [`<span>${companyName}</span>`];
  if (item.history) {
    // 历史标签处理，针对黑名单的需要单独做下处理
    let role = item.role;
    switch (options.key) {
      case 'Shareholder':
      case 'ShareholdingRelationship':
        role = '历史股东';
        break;
      case 'ForeignInvestment':
      case 'InvestorsRelationship':
        role = '历史对外投资';
        break;
    }
    if (role) {
      elements.push(`<span class="status-tag default">${role}</span>`);
    }
  }
  return elements.join('');
};

/**
 * 持股比例
 */
export function renderStockPercent(stockpercent?: number) {
  if (stockpercent === undefined) {
    return '-';
  }
  return `${stockpercent}%`;
}

export function blacklistDurationFormatter(duration) {
  if (duration === undefined || duration === null) {
    return '-';
  }

  const BLACKLIST_DURATION_MAP = {
    '0': '3个月',
    '1': '6个月',
    '2': '1年',
    '3': '2年',
    '4': '3年',
    '5': '5年',
    '-1': '不限',
  };

  // duration后端返回了两种，一种是时间戳，一种是type，type只有0-8，大于100默认它是时间戳，做处理
  if (duration > 100) {
    return moment
      .duration(duration * 1000)
      .locale('zh-Cn')
      .humanize();
  }
  return BLACKLIST_DURATION_MAP[duration] || '-';
}

/**
 * 相同实际控制人关联关系（控制路径/控制链）
 */
export function controlRelationsPath(_, item, originalData) {
  const paths = item?.details?.path || [];
  if (paths.length === 0) {
    return '';
  }
  return paths
    .map((pList, index) => {
      const description = `控制路径${index + 1}（占比约 ${pList[pList.length - 1]?.PercentTotal} ）`;
      const entities = pList.map((p) => `（${p.Percent}） → ${p.Name}`);
      // 添加实际控制人及其权占比
      const graph = [`${item.name}`, ...entities].join('');
      return `<div>
            <div style="font-weight: bold;">${description}</div>
            <div>${graph}</div>
          </div>`;
    })
    .join('');
}

/**
 * 潜在利益冲突
 */
export function interestConflictStaffColumnRender(_, item) {
  // 关联人的样式
  const formRelate = () => {
    if (item.relationPersonKeyNo) {
      return `
            <spain>
              ${item.relationPersonName}
              -
            </spain>
          `;
    }
    return `
          <span>
            ${item.relationPersonName}
            -
          </span>
        `;
  };
  const getPNO = () => {
    const pNo = item.personNo;
    if (item.relationPersonId && item.relationPersonId !== -1) {
      const name = pNo.split(`_${item.relationship}`)[0];
      return `
            <span>
              <span>${name}</span>
              -
              ${formRelate()}
              <span>${item.name}</span>
              <span>(${item.relationship})</span>
            </span>
          `;
    }
    return `
          <span>
            ${pNo} - ${item.name}
          </span>
        `;
  };
  return `
        <span>
          ${getPNO()}
          ${item.status === 1 ? '是本人' : ''}
        </span>
      `;
}

/**
 * 涉诉围串标记录-当事人
 */
export function casePartiesColumnRender(_, item) {
  const getStyleByJudgeDescription = (text: string) => {
    if (['支持', '解除财产保全', '对方被驳回', '执行完毕', '申请人被驳回', '部分支持', '同意追加被执行人'].includes(text)) {
      return 'color: #0aad65';
    }
    if (['驳回', '不支持', '驳回上诉', '财产保全', '不支持', '终结本次执行'].includes(text)) {
      return 'color: #F04040';
    }
    return 'color: #999';
  };
  // 获取当事人信息
  const getParties = (_item) => {
    if (_item.caserolegroupbyrolename?.length) {
      return _item.caserolegroupbyrolename
        ?.filter((item2) => item2.LawyerTag === 0 || isNil(item2.LawyerTag))
        ?.flatMap((item2) =>
          item2.DetailList?.map((item3) => ({
            ...item3,
            Role: item2.Role,
          })),
        );
    }

    if (_item.caserole?.length) {
      return _item.caserole.map((item2) => ({
        ...item2,
        Role: item2.R,
        Name: item2.P || item2.ShowName,
        KeyNo: item2.N,
        Org: item2.O,
      }));
    }
    return [];
  };

  const getStyleByJob = (job: string) => {
    if (job === '企业主体') {
      return `color: #666; background: #eee`;
    }
    return `color: #bb833d; background: #f6f0e7`;
  };

  // const parties = [...getParties(item)];

  const parties = [
    ...getParties(item),
    ...(item.involveRole || []).map((role) => ({
      ...role,
      Role: role.Tag,
    })),
  ];

  if (item?.involveRole?.length) {
    parties.push(
      ...item.involveRole.map((role) => ({
        ...role,
        Role: role.Tag,
      })),
    );
  }

  if (!parties?.length) {
    return '-';
  }

  let content = '';

  parties.forEach((item) => {
    content += `<div>`;
    // 角色 - 名称
    content += `<span>${item?.Role} - ${item?.Name}</span>`;

    // 描述
    if (item.JudgeResultDescription) {
      content += ` <span style="${getStyleByJudgeDescription(item.JudgeResultDescription)}">[${item.JudgeResultDescription}]</span>`;
    }

    // 角色
    if (item.Job) {
      content += ` <span style="${getStyleByJob(item.Job)}">${item.Job}</span>`;
    }

    content += `</div>`;
  });

  return content;
}

/**
 * 直接关系-关联详情链
 */
export function relationsPathParser(relations): string {
  if (!Array.isArray(relations) || relations.length === 0) {
    return '-';
  }

  const nodes: string[] = [];

  relations.forEach((item) => {
    if (isEdge(item)) {
      nodes.push(createEdge(item));
    } else {
      nodes.push(`<span>${item['Company.name'] || item['Person.name']}</span>`);
    }
  });

  return nodes.join('') || '-';
}

/**
 * 获取全部角色
 */
const correctRoles = (relations) => {
  let roles: string[] = [];
  relations.forEach((relation) => {
    if (!isEdge(relation)) {
      return;
    }

    if (Array.isArray(relation.roles)) {
      // 嵌套数组
      roles = [...roles, ...relation.roles.map((role) => role.type)];
    } else {
      roles = [...roles, relation.type && !['0', '1'].includes(relation.type) ? relation.type : relation.role];
    }
  });
  return roles;
};

/**
 * 疑似关系-关联详情链
 */
export function relationsPathParser2(record): string {
  if (record.relations?.length) {
    return record.relations
      ?.map((relations, index, source) => {
        // data 包含的额外信息（例如：返回的结果数量，用来展示更多信息、弹窗等）
        const data = record.data[index];
        const roles = correctRoles(relations);
        const levelTag = getRelationTag(roles);

        // 关联路径
        if (data === null) {
          // 显示路径
          const step = source.length > 1 ? `<div style="font-weight: 700;">路径：${index + 1}</div>` : '';
          return step + relationsPathParser(relations) + levelTag;
        }

        // 从 Edge 中提取关联信息
        const roleInfo = relations.find((v) => v?.role) || {};

        if (Array.isArray(data)) {
          // { key: 'ContactNumber', name: '相同电话号码' },
          // { key: 'Website', name: '相同域名信息' },
          // { key: 'Address', name: '相同经营地址' },
          // { key: 'Mail', name: '相同邮箱' },
          // { key: 'Patent', name: '相同专利信息' },
          // { key: 'IntPatent', name: '相同国际专利信息' },
          // { key: 'SoftwareCopyright', name: '相同软件著作权' },
          const dataList = data.map((listItem) => {
            // 展示内容：相同邮箱
            if (isString(listItem)) {
              return listItem;
            }
            if (listItem?.t) {
              return listItem.t;
            }
            if (listItem?.address) {
              return listItem.address;
            }
            // 展示内容：相同电话号码
            return String(listItem);
          });

          const countText = roleInfo.role ? `${roleInfo.role}: ` : null;
          const result = `${countText}${uniq(dataList).join('、')}` || '-';
          return result + levelTag;
        }

        if (isObject(data)) {
          const total = get(data, 'Paging.TotalRecords', 0);
          const countText = `${roleInfo.role || ''}数量: ${total}`;
          return countText + levelTag;
        }

        return '-';
      })
      .join('<br />');
  }

  return '-';
}

/**
 * 关联关系
 */
export function relationsTypeParser(record) {
  // 遍历关系链上所有角色
  const roles = record?.relations?.flatMap((relations, index) => {
    const relationsInfo = relations.map((node) => {
      // record.data 为对象时，仅显示第一层关系
      if (isObject(record?.data?.[index])) {
        const edgeInfo = relations.find((v) => v?.role) || {};
        return edgeInfo.role;
      }
      if (node.type === 'UpAndDownRelation') {
        return '上下游关系';
      }
      return node.role;
    });
    return relationsInfo;
  });
  return union(roles.filter(Boolean)).join('<br />');
}

/**
 * 与内部黑名单列表存在分支机构关联
 * @param item
 * @returns
 */
export function companyBranchPath(item) {
  const role = item.role;

  const path = [item.companyNameDD, item.companyNameRelated];

  const pathArr = item.direction > 0 ? path : reverse(path);

  return pathArr.join(`<span> -（${role}）→ </span>`);
}

export const successStatusTxt = [
  '在业',
  '存续',
  '筹建',
  '新申请用户',
  '已成立事先报批',
  '成立事先报批中',
  '成立中',
  '名称核准发起中',
  '名称核准通过',
  '已成立',
  '正常',
  '仍注册',
  '接管',
  '核准设立',
  '核准认许',
  '核准许可登记',
  '核准许可',
  '核准报备',
  '核准许可报备',
  '核准登记',
  '有效',
  '核准設立',
  '核准認許',
  '核准許可登記',
  '核准許可',
  '核准報備',
  '核准許可報備',
  '核准登記',
  '核準設立',
  '核準認許',
  '核準許可登記',
  '核準許可',
  '核準報備',
  '核準許可報備',
  '核準登記',
  'ACTIVE',
  'CONVERTED',
  'INCORPORATED',
  'MERGED',
  'OTHERS',
  'PERPETUAL',
  'REDEEMED',
  'UNKNOWN',
  'AMALGAMATED',
  'IN BUSINESS',
  'RESERVED',
  'CONVERSION',
  'RE-INSTATEMENT',
  '存续（在营、开业、在册）',
  '迁出',
  '迁入',
];

export const dangerStatusTxt = [
  '清算',
  '撤销',
  '责令关闭',
  '吊销',
  '已撤销',
  '终止破产',
  '涂销破产',
  '清理完结',
  '清理',
  '破产清算完结',
  '破产程序终结',
  '破产',
  '废止清算完结',
  '废止许可完结',
  '废止许可',
  '废止认许',
  '废止认许完结',
  '废止登记完结',
  '废止登记',
  '废止',
  '撤销完结',
  '撤销无需清算',
  '撤销许可',
  '撤销',
  '撤销认许',
  '撤销认许完结',
  '撤回认许',
  '撤回认许完结',
  '无效',
  '終止破產',
  '涂銷破產',
  '清理完結',
  '破產清算完結',
  '破產程序終結',
  '破產',
  '廢止清算完結',
  '廢止許可完結',
  '廢止許可',
  '廢止認許完結',
  '廢止登記完結',
  '廢止登記',
  '廢止',
  '撤銷完結',
  '撤銷無需清算',
  '撤銷許可',
  '撤銷',
  '撤銷認許',
  '撤銷認許完結',
  '撤回認許',
  '撤回認許完結',
  'ABANDONED',
  'CANCELED',
  'CANCELLED',
  'DELINQUENT',
  'DISSOLVED',
  'EXPIRED',
  'FORFEITED',
  'INACTIVE',
  'REMOVED',
  'SUSPENDED',
  'TERMINATED',
  'WITHDRAWN',
  'REVOKED',
  'LIQUIDATION',
  'STRIKE OFF',
  'STRIKING OFF',
  'DEFUNCT',
  'NOT AVAILabel',
  'DORMANT',
  'CAPTURED',
  'DEREGISTRATION',
  'DUPLICATE',
  'DEREGISTERED',
  'NO STATUS',
  'ARCHIVE',
  '撤銷',
  '廢止',
  '除名',
];

export const warningStatusTxt = [
  '注销',
  '停业',
  '歇业',
  '已告解散',
  '已终止注册',
  '已終止註冊',
  '停業',
  '名称核准不通过',
  '注销中',
  '已终止营业地点',
  '不再是独立的实体',
  '休止活动',
  '重整',
  '解散',
  '解散清算完结',
  '设立但已解散',
  '合并解散',
  '分割解散',
  '撤销设立',
  '撤销登记完结',
  '撤销登记',
  '撤回登记',
  '撤回登记完结',
  '解散清算完結',
  '設立但已解散',
  '合併解散',
  '撤銷設立',
  '撤銷登記完結',
  '撤銷登記',
  '撤回登記完結',
  '撤回登記',
  '撤銷設立',
  '撤銷登記完结',
  '撤銷登記',
  '撤回登記完结',
  '撤回登记',
  '設立但已解散',
  '合並解散',
  '解散清算完結',
  '经营异常',
  '裁判文书',
  '严重违法',
  '失信被执行人',
  '税收违法',
  '行政处罚',
  '开庭公告',
];

export enum CompanyStatus {
  Danger = 'danger',
  Warning = 'warning',
  Primary = 'primary',
  Success = 'success',
  Default = 'shade',
}

export const getCompanyStatusColor = (status) => {
  let result = CompanyStatus.Default;
  if (dangerStatusTxt.includes(status)) {
    result = CompanyStatus.Danger;
  } else if (warningStatusTxt.includes(status)) {
    result = CompanyStatus.Warning;
  } else if (successStatusTxt.includes(status)) {
    result = CompanyStatus.Success;
  }
  return result;
};

function generateCompanyStatusTag(status: string) {
  const defaultTagType = getCompanyStatusColor(status);
  return `<span class="status-tag ${defaultTagType}">${status}</span>`;
}

/**
 * 企业登记状态
 */
export function companyStatusParser(record, dataIndex) {
  const statusText = record[dataIndex];
  if (!statusText) {
    return '-';
  }
  return generateCompanyStatusTag(statusText);
}

/**
 * 关联方类型
 */
export function relatedTypeDescParser(record) {
  const codeList = record.relatedTypes || [];
  const textList = record.relatedTypeDescList || [];
  if (!codeList?.length) {
    return '-';
  }
  return textList
    .filter((item) => !!item)
    .map((item, index) => {
      if (['HasPhone', 'HasAddress'].includes(codeList[index]) && record.contactList?.length) {
        const text = record.contactList.join('、');
        return `<div>${item} （${text}）</div>`;
      }
      return `<div>${item}</div>`;
    })
    .join('');
}

/**
 * 信息类型
 */
export function riskTypeParser(record) {
  if (isArray(record.riskTypeInfos) && record.riskTypeInfos?.length) {
    return record.riskTypeInfos
      .map((item) => {
        return generateCompanyStatusTag(item.riskTypeDesc);
      })
      .join('');
  }
  // 兼容历史数据
  if (!isArray(record.riskTypeDescList) || !record.riskTypeDescList?.length) {
    return '-';
  }
  const data = uniq(record.riskTypeDescList).filter((item) => !isNil(item));
  return data
    .map((item: string) => {
      return generateCompanyStatusTag(item);
    })
    .join('');
}

/**
 * 获取关联路径
 * 由于接口返回的paths包含所有路径，需要根据 {level: '1'} 来截取路径
 * @param paths 格式：[{level: '1'}, {level: '2'}, {level: '3'}, {level: '1'}, {level: '2'}, {level: '1'}]
 * @param fromIndex 记录上次截取的下标，从上次结束的地方开始截取
 *
 * [{level: '1'}, {level: '2'}, {level: '1'}]
 */
const getPartialPath = (pathList: Record<string, any>[], fromIndex = 0): [number, Record<string, any>[]] => {
  const restPart = pathList.slice(fromIndex + 1);
  const index = restPart.findIndex((item) => item.level?.toString() === '1');
  const endIndex = index >= 0 ? index + fromIndex + 1 : pathList.length;
  return [endIndex, pathList.slice(fromIndex, endIndex)];
};

const getInvestmentPath = (startInfo: { name: string; keyNo: string }, paths = []) => {
  const startCompany = {
    Name: startInfo.name,
    KeyNo: startInfo.keyNo,
    Level: 0,
    Percent: 0,
    PercentTotal: 0,
  };

  let startIndex = 0;
  const pathData = paths.map((item) => {
    const [endIndex, realPaths] = getPartialPath(item, startIndex);
    startIndex = endIndex;
    const fullPath = [startCompany, ...realPaths];
    const percentTotal = realPaths.map((v) => new Big(parseFloat(v.percent)).div(100)).reduce((a, b) => a.times(b));
    return fullPath.map((data: any, idx: number) => {
      let underStr;
      const nextPath = fullPath[idx + 1] as any;
      if (nextPath) {
        underStr = nextPath?.Percent || nextPath?.percent;
      } else {
        underStr = data.Percent || data.percent;
      }

      return {
        ...data,
        Name: data.Name || data.name,
        KeyNo: data.KeyNo || data.keyNo,
        Level: data.Level || data.level,
        underStr,
        PercentTotal: `${parseFloat(percentTotal.times(100).toFixed(4))}%`,
      };
    });
  });
  return pathData;
};

/**
 * 投资链
 */
export function investmentPathParser(record) {
  if (!record.paths?.length) {
    return '-';
  }
  const paths = getInvestmentPath({ name: record.operName, keyNo: record.operKeyNo }, record.paths);

  // return JSON.stringify(paths);
  const pathArr = paths.map((pList, index) => {
    let percentTotal;
    const entities: string[] = [];
    pList.forEach((p, j) => {
      entities.push(`${p.percent ? `（${p.percent}）` : ''} ${p.Name}`);
      if (j === pList.length - 1) {
        percentTotal = p.PercentTotal; // NOTE: 取路径中最后一个 `PercentTotal`
      }
    });
    const description = `路径${index + 1}（占比约 ${percentTotal} ）`;
    const graph = [...entities].join(' → ');
    return `<div>
      <div style="font-weight: bold;">${description}</div>
      <div>${graph}</div>
    </div>`;
  });
  if (paths.length >= 10) {
    pathArr.push(`<div style="color: #999">*仅显示TOP10路径</div>`);
  }
  return pathArr.join('');
}
