import * as moment from 'moment';
import {
  blacklistParser,
  companyStatusParser,
  dateParser,
  diffParser,
  entitiesParser,
  getRelationTag,
  hanlePatentStatusColorParser,
  investmentPathParser,
  jsonParser,
  judicialAuctionParser,
  moneyParser,
  moneyWithUnitParser,
  numberToHuman,
  pathParser,
  rangeParser,
  relatedTypeDescParser,
  riskLevelParser,
  riskTypeParser,
  statusParser,
  templateParser,
} from './pdf-table.util';
import { get, isObject, omit, orderBy, toArray } from 'lodash';
import { OperInfoMapping } from 'libs/constants/common';

export const TableColumnRenderer = {
  date: dateParser,
  money: moneyParser,
  moneyWithUnit: moneyWithUnitParser,
  entities: entitiesParser,
  json: jsonParser,
  status: statusParser,
  diff: diffParser,
  judicialAuction: judicialAuctionParser,
  path: pathParser,
  blacklist: blacklistParser,
  template: templateParser,
  range: rangeParser,
  riskLevel: riskLevelParser,
  companyStatus: companyStatusParser,
  relatedTypeDesc: relatedTypeDescParser,
  riskType: riskTypeParser,
  investmentPath: investmentPathParser,
  hanlePatentStatusColor: hanlePatentStatusColorParser,
};

/** 注册资本文案映射 */
const translateRegistCapiLabel = (enterpriseType: string[] | string) => {
  const defaultLabel = '注册资本';
  const enterpriseTypeToLabelMap: Record<string, string> = {
    '001006': '出资额',
    '*********': '出资额',
    '*********': '出资额',
    '001015': '出资额',
    '001009': '资金数额',
    '001008': '成员出资总额',
    '001004': '开办资金',
    '001005': '注册资金',
    org: '注册资金',
  };
  if (Array.isArray(enterpriseType)) {
    const type = enterpriseType.find((v) => enterpriseTypeToLabelMap[v]);
    return enterpriseTypeToLabelMap[type] || defaultLabel;
  }
  return enterpriseTypeToLabelMap[enterpriseType] || defaultLabel;
};

/**
 * 注册渲染模板工具函数
 */
export function templateHelperRegister(hbs, settings: { tableColumns: Record<string, unknown> }) {
  // console.log('register template helper');

  const DEFAULT_PLACE_HOLDER = '-';

  /**
   * 判断给定两值是否相等
   */
  hbs.registerHelper('is_equal', function (a, b) {
    return a === b;
  });

  /**
   * 判断给定两值A是否大于B
   */
  hbs.registerHelper('is_greater_than', function (a, b) {
    return a > b;
  });

  /**
   * 占位符
   */
  hbs.registerHelper('placeholder', function (value, options) {
    return value || options.hash.placeholder || DEFAULT_PLACE_HOLDER;
  });

  /**
   * 数字格式化
   */
  hbs.registerHelper('number_formatter', function (value, precision = 2, reduce = 1) {
    return numberToHuman(value / reduce, {
      precision,
    });
  });

  /**
   * 时间戳转化
   */
  hbs.registerHelper('date_format', function (value, options) {
    if (!value) {
      return options.hash.default || '-';
    }
    return moment(value, options.hash.pattern || 'X').format(options.hash.format || 'YYYY-MM-DD');
  });

  /**
   * 当前日期
   */
  hbs.registerHelper('date_now', function (format) {
    return moment().format(format);
  });

  /**
   * 文本替换
   */
  hbs.registerHelper('replace_with', function (content, replace, placeholder = DEFAULT_PLACE_HOLDER) {
    return content ? content.replace(replace, '') : placeholder;
  });

  /**
   * 限制数组长度
   */
  hbs.registerHelper('limit_by', function (list: any[], options) {
    if (!options.hash.size) {
      return list;
    }
    return Array.isArray(list) ? list.slice(0, options.hash.size) : list;
  });

  /**
   * 排序
   */
  hbs.registerHelper('order_by', function (list: any[], options) {
    return orderBy(list, [options.hash.field], [options.hash.order ?? 'desc']);
  });

  /**
   * 通过字段路径获取对象的值
   */
  hbs.registerHelper('get_value_by_key', (object: Record<string, unknown>, keyPath: string, options?) => {
    const defaultValue = options ? options?.hash?.default : '';
    if (!object) {
      return defaultValue;
    }
    const result = get(object, keyPath, defaultValue);
    return result;
  });

  hbs.registerHelper('to_array', (object: Record<string, unknown>) => {
    if (!object) {
      return [];
    }
    return [object];
  });

  /**
   * 计算给定数组长度
   */
  hbs.registerHelper('size', (list: unknown[]) => {
    if (!Array.isArray(list)) {
      return 0;
    }
    return list.length;
  });

  hbs.registerHelper('incremented', function (n: number, increment) {
    return n + increment;
  });

  hbs.registerHelper('decremented', function (n: number, increment) {
    return n - increment;
  });

  /**
   * 维度处理
   */
  hbs.registerHelper('get_data_source_by_hit_details', function (hitDetails, dimensions = {}) {
    const { dimensionKey, strategyId } = hitDetails;
    const hitKey = `${dimensionKey}-${strategyId}`;
    const hitResult = get(dimensions, [hitKey, 'data'], dimensions[dimensionKey]);
    return hitResult || [];
  });

  /**
   * 表格处理
   */
  hbs.registerHelper('get_columns_by_dimension_key', function (dimensionKey: string, hitDetails?, dimensions?) {
    let columnsKey = dimensionKey;
    if (hitDetails && dimensions) {
      const { dimensionKey: newDimensionKey, strategyId } = hitDetails;
      const hitKey = `${newDimensionKey}-${strategyId}`;
      columnsKey = get(dimensions, [hitKey, 'displayKey']) || columnsKey;
    }
    const columns = settings.tableColumns[columnsKey] || [];
    return columns;
  });

  /**
   * 类数据对象结构处理
   */
  hbs.registerHelper('parse_array_like_object', function (data: Record<string, any>[]) {
    const result = [];

    /**
     * 将类数组对象转化为数组
     * @param arrayLikeObject
     */
    const parseArrayLikeObject = (arrayLikeObject: Record<string, any>) => {
      return toArray(omit(arrayLikeObject, 'recordId'));
    };

    data.forEach((item) => {
      if (isObject(item)) {
        result.push(parseArrayLikeObject(item));
      } else {
        result.push(item);
      }
    });

    return result;
  });

  /**
   * 表格列宽处理
   */
  hbs.registerHelper('render_table_col_column', function (columnConfig) {
    let width: string;
    if (typeof columnConfig?.width === 'number') {
      width = `${columnConfig.width}px`;
    } else if (typeof columnConfig?.width === 'string') {
      width = columnConfig.width;
    }

    const cssStyle = width ? `width: ${width};` : '';
    return `<col style='${cssStyle}' />`;
  });

  hbs.registerHelper('render_table_header_column', function (columnConfig) {
    return `<th style='${columnConfig?.hdStyle || ''}'>${columnConfig.title}</th>`;
  });

  hbs.registerHelper('render_table_column', function (columnConfig, dataRow, originalData, index, dataSource) {
    let content = '';
    // 直接取 dataIndex 对应的值
    if (!columnConfig.customRender) {
      content = get(dataRow, columnConfig.dataIndex);
    } else if (typeof columnConfig.customRender === 'function') {
      /**
       * customRender 函数
       * @param column 对应的列值
       * @param dataRow 原始值
       */
      const dataIndex = columnConfig.dataIndex;
      content = columnConfig.customRender.apply(null, [dataIndex !== undefined ? dataRow[dataIndex] : dataRow, dataRow, originalData]);
    } else if (typeof TableColumnRenderer[columnConfig.customRender.name] === 'function') {
      // customRender 通用配置
      const fn = TableColumnRenderer[columnConfig.customRender.name];
      content = fn.apply(null, [dataRow, columnConfig.dataIndex, columnConfig.customRender.options]);
    }
    const attrsConfig = typeof columnConfig.attrs === 'function' ? columnConfig.attrs(dataRow, index, dataSource) : columnConfig.attrs;
    const attrs = attrsConfig
      ? Object.entries(attrsConfig)
          .map(([k, v]) => `${k}="${typeof v !== 'string' ? JSON.stringify(v) : v}"`)
          .join('')
      : '';
    return `<td ${attrs}>${content || columnConfig.placeholder || DEFAULT_PLACE_HOLDER}</td>`;
  });

  hbs.registerHelper('get_bidding_relation_level_tag', function (item) {
    return getRelationTag(item.hitType?.length ? item.hitType : item.key);
  });

  hbs.registerHelper('get_business_term_status_tag', function (termStatus) {
    const statusMap = {
      临近到期: 'warning',
      已到期: 'danger',
    };
    return `<span class="term-status-tag ${statusMap[termStatus]}">${termStatus}</span>`;
  });

  hbs.registerHelper('get_regist_capi_label', function (item) {
    if (item) {
      const isOrg = item?.KeyNo?.startsWith('s');
      return translateRegistCapiLabel(isOrg ? 'org' : item.standardCode);
    }
    return '-';
  });

  hbs.registerHelper('and', function (a, b) {
    return a && b;
  });

  hbs.registerHelper('or', function (a, b) {
    return a || b;
  });

  hbs.registerHelper('json_view', function (a) {
    return JSON.stringify(a, null, 2);
  });

  hbs.registerHelper('get_org_model_risk_level_name', function (orgModel, level) {
    const levelInfo = orgModel.resultSetting.find((item) => item.level === level);
    return levelInfo.name;
  });

  hbs.registerHelper('object_keys', function (obj) {
    return Object.keys(obj);
  });

  hbs.registerHelper('get_empty_shell_sub_title', function (key: string) {
    const SUB_DIMENSION_NAME = {
      histShareHolderList: '历史股东',
      historyEmployeeList: '历史高管',
      historyOperList: '历史法定代表人',
    };

    return SUB_DIMENSION_NAME[key];
  });

  hbs.registerHelper('get_oper_name_by_type', function (companyInfo, defaults = '法定代表人') {
    if (!companyInfo?.Oper?.OperType) {
      return defaults || '-';
    }
    return OperInfoMapping.OperType[companyInfo.Oper.OperType] || defaults || '-';
  });

  /**
   * 通过聚合子集计算风险数量
   */
  hbs.registerHelper('get_agg_risk_count', (list: Record<string, unknown>[], keyPath: string, options?) => {
    const defaultValue = options ? options?.hash?.default : 0;
    if (!Array.isArray(list) || !list.length) {
      return defaultValue;
    }

    return list.reduce((acc, cur) => {
      const value = get(cur, keyPath, []);
      if (Array.isArray(value)) {
        return acc + value.length;
      }
      return acc;
    }, 0);
  });

  return hbs;
}
