import { RedisService } from '@kezhaozhao/nestjs-redis';
import { PaginationResponse } from '@kezhaozhao/qcc-model';
import { forwardRef } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppTestModule } from 'apps/app/app.test.module';
import { BatchModule } from 'apps/batch/batch.module';
import { BatchInfoPO } from 'apps/batch/model/BatchInfoPO';
import { BatchCreatorHelperBase } from 'apps/batch/service/helper/batch.creator.helper.base';
import { CompanySearchModule } from 'apps/company/company-search.module';
import { DiligenceModule } from 'apps/diligence/diligence.module';
import { EvaluationService } from 'apps/diligence/evaluation/evaluation.service';
import { DiligenceSnapshotService } from 'apps/diligence/snapshot/diligence.snapshot.service';
import { SnapshotQueueTypeEnums } from 'apps/diligence/snapshot/po/SnapshotQueueTypeEnums';
import { MonitorCompanyService } from 'apps/monitor/company/monitor.company.service';
// import { MonitorDynamicMessageListener } from 'apps/monitor/dynamic/monitor.dynamic.message.listener';
import { MonitorGroupService } from 'apps/monitor/group/monitor.group.service';
import { MonitorModule } from 'apps/monitor/monitor.module';
import { ModelInitICBCSZMonitorService } from 'apps/risk_model/init_mode/model.init.monitor.icbc.sz.service';
import { ModelInitMonitorService } from 'apps/risk_model/init_mode/model.init.monitor.service';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import * as Bluebird from 'bluebird';
import { BatchDiligenceEntity } from 'libs/entities/BatchDiligenceEntity';
import { BatchEntity } from 'libs/entities/BatchEntity';
// import { DatasetEntity } from 'libs/entities/DatasetEntity';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { DimensionHitStrategyEntity } from 'libs/entities/DimensionHitStrategyEntity';
import { MonitorGroupEntity } from 'libs/entities/MonitorGroupEntity';
import { QaCompanyAnnotatedTestEntity } from 'libs/entities/QaComapnyAnnotatedTestEntity';
import { QaCompanyLabelEntity } from 'libs/entities/QaComapnyLabelEntity';
import { QaCompanyEntity } from 'libs/entities/QaCompanyEntity';
import { QaDatasetEntity } from 'libs/entities/QaDatasetEntity';
import { QaDatasetItemEntity } from 'libs/entities/QaDatasetItemEntity';
import { QaLabelEntity } from 'libs/entities/QaLabelEntity';
import { QaTaskEntity } from 'libs/entities/QaTaskEntity';
import { QaTaskResultAnalyzedEntity } from 'libs/entities/QaTaskResultAnalyzedEntity';
import { QaTaskResultEntity } from 'libs/entities/QaTaskResultEntity';
import { RiskModelEntity } from 'libs/entities/RiskModelEntity';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { ProductCodeEnums } from 'libs/enums/ProductCodeEnums';
import { RiskModelTypeEnums } from 'libs/enums/RiskModelTypeEnums';
import { PlatformUser } from 'libs/model/common';
import { AddMonitorCompanyItemPO } from 'libs/model/monitor/AddMonitorCompanyRequest';
import * as moment from 'moment';
import { EntityManager } from 'typeorm';
import { DatasetService } from './dataset.service';
import { QaTaskResultGroupEnums } from './model/enums/QaTaskResultGroupEnums';
import { QaTaskTypeEnums } from './model/enums/QaTaskTypeEnums';
import { QaTypeEnums } from './model/enums/QaTypeEnums';
import { CreateQaCompayResponseItem } from './model/po/CreateQaCompayResponseItem';
import { QaCompanyService } from './qa.company.service';
import { QaLabelService } from './qa.label.service';
import { QaTaskAnalyzerService } from './qa.task.analyzer.service';
import { QaTaskResultService } from './qa.task.result.service';
import { QaTaskService } from './qa.task.service';
import { LoginFilterOperator } from './model/request/SearchQaTaskResultStatisticsRequest';
import { MonitorCompanyEntity } from 'libs/entities/MonitorCompanyEntity';
import { MonitorDynamicMessageListenerV2 } from '../monitor/dynamic/process/monitor-dynamic-message-listener-v2';

jest.setTimeout(1000000);
describe('QaTaskService Integration Test', () => {
  let module: TestingModule;
  let entityManager: EntityManager;
  let qaComapnyService: QaCompanyService;
  let datasetService: DatasetService;
  let qaTaskService: QaTaskService;
  let evaluationService: EvaluationService;
  let monitorDynamicService: MonitorDynamicMessageListenerV2;
  let monitorGroupService: MonitorGroupService;
  let monitorCompanyService: MonitorCompanyService;
  let modelInitMonitorService: ModelInitMonitorService;
  let batchCreatorHelperService: BatchCreatorHelperBase;
  let modelInitICBCSZMonitorService: ModelInitICBCSZMonitorService;
  let snapshotService: DiligenceSnapshotService;
  let redisService: RedisService;
  let qaTaskResultService: QaTaskResultService;
  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('qa.task.service.integration.spec.ts');
  const testUser: PlatformUser = getTestUser(testOrgId, testUserId, ProductCodeEnums.Pro);

  /**
   * 创建 Batch
   */
  const createBatch = async (
    riskModelId: number,
    { batchStartTime, batchEndTime }: Pick<BatchInfoPO, 'batchStartTime' | 'batchEndTime'>,
  ): Promise<BatchEntity> => {
    const batchInfo: BatchInfoPO = {
      // asyncBatch: 1, // 标记为异步启动批次，需要后续手动启动
      orgModelIds: [riskModelId],
      cacheHours: 0,
      currentUser: testUser,
      batchStartTime,
      batchEndTime,
      batchComment: 'QA测试任务-批量 尽调/监控排查',
    };
    const batch = Object.assign(new BatchEntity(), {
      product: testUser.currentProduct,
      orgId: testUser.currentOrg,
      createBy: testUser.userId,
      createDate: new Date(),
      businessType: BatchBusinessTypeEnums.Diligence_Continuous,
      batchInfo: batchInfo,
      recordCount: 0,
      statisticsInfo: {
        totalCount: 0,
        placeholderCount: 0,
        nonRepeatableCount: 0,
      },
    });
    const batchEntity = await entityManager.save(batch);
    return batchEntity;
  };

  /**
   * 执行尽调
   */
  const doDiligence = async (
    modelId: number,
    batchId: number,
    companies: {
      companyId: string;
      companyName: string;
    }[],
    user: PlatformUser,
  ) => {
    const diligenceList = await Bluebird.map(
      companies,
      async (company) => {
        const diligence = await evaluationService.runMonitorRisk(
          user.currentOrg,
          {
            companyId: company.companyId,
            companyName: company.companyName,
            orgModelIds: [modelId],
            batchId,
          },
          ProductCodeEnums.Pro,
        );

        // NOTE: 保存 batch_diligence 用于分析命中情况 QaTaskService.analyze -> generatedQaTaskResultForEvaluation
        const diligenceId = diligence.id;
        const batchDiligence = await entityManager.findOne(BatchDiligenceEntity, {
          where: {
            batchId: batchId,
            diligenceId,
          },
        });
        if (!batchDiligence) {
          await entityManager.save(
            Object.assign(new BatchDiligenceEntity(), {
              batchId: batchId,
              jobId: 0,
              diligenceId,
            }),
          );
        }

        console.log('DILIGENCE_BATCH_ID', batchDiligence, batchId);
        return diligence;
      },
      { concurrency: 2 },
    );
    return diligenceList;
  };

  /**
   * 执行监控
   */
  const doMonitor = async (
    companyItems: {
      companyId: string;
      companyName: string;
    }[],
    modelId: number,
    user: PlatformUser,
  ): Promise<BatchEntity> => {
    // 第一次batch
    const preBatchEntity = await createBatch(modelId, {
      batchStartTime: moment().subtract(3, 'year').unix(), // 设置动态时间区间范围
      batchEndTime: moment().subtract(1, 'year').unix(),
    });

    // 第一次对监控分组内的企业执行尽调
    const preDiligenceList = await doDiligence(modelId, preBatchEntity.batchId, companyItems, user);
    console.log('preDiligenceList', preDiligenceList);

    return preBatchEntity;
  };

  const qaCompanies: AddMonitorCompanyItemPO[] = [
    {
      companyId: 'f5ec123eecfab8e3b1db5eb416b35cf2', //融创房地产集团有限公司
      companyName: '融创房地产集团有限公司',
    },
    {
      companyId: '9a9717085199738f7e30b2c63154b40c', // 深圳艾萨克博商贸有限公司
      companyName: '深圳艾萨克博商贸有限公司',
    },
    {
      companyId: '1604a34288f34b1cbc0dc2042257c33b', // 国通信托有限责任公司
      companyName: '国通信托有限责任公司',
    },
    {
      companyId: '4c5563ba22399eeef34cce28cf4227c6', // 中国人寿保险股份有限公司江苏省分公司
      companyName: '中国人寿保险股份有限公司江苏省分公司',
    },
  ];

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        AppTestModule,
        TypeOrmModule.forFeature([
          QaDatasetEntity,
          QaDatasetItemEntity,
          QaCompanyEntity,
          QaTaskEntity,
          QaTaskResultEntity,
          QaCompanyLabelEntity,
          QaLabelEntity,
          MonitorGroupEntity,
          BatchEntity,
          BatchDiligenceEntity,
          QaCompanyAnnotatedTestEntity,
          DimensionHitStrategyEntity,
          BatchEntity,
          QaTaskResultAnalyzedEntity,
          RiskModelEntity,
          MonitorCompanyEntity,
        ]),
        CompanySearchModule,
        MonitorModule,
        forwardRef(() => BatchModule),
        DiligenceModule,
      ],
      providers: [DatasetService, QaTaskService, QaCompanyService, QaLabelService, QaTaskAnalyzerService, QaTaskResultService],
    }).compile();

    entityManager = module.get<EntityManager>(EntityManager);
    qaComapnyService = module.get<QaCompanyService>(QaCompanyService);
    datasetService = module.get<DatasetService>(DatasetService);
    qaTaskService = module.get<QaTaskService>(QaTaskService);
    evaluationService = module.get<EvaluationService>(EvaluationService);
    modelInitMonitorService = module.get<ModelInitMonitorService>(ModelInitMonitorService);
    batchCreatorHelperService = module.get<BatchCreatorHelperBase>(BatchCreatorHelperBase);
    snapshotService = module.get<DiligenceSnapshotService>(DiligenceSnapshotService);
    redisService = module.get<RedisService>(RedisService);
    monitorGroupService = module.get<MonitorGroupService>(MonitorGroupService);
    monitorCompanyService = module.get<MonitorCompanyService>(MonitorCompanyService);
    monitorDynamicService = module.get<MonitorDynamicMessageListenerV2>(MonitorDynamicMessageListenerV2);
    modelInitICBCSZMonitorService = module.get<ModelInitICBCSZMonitorService>(ModelInitICBCSZMonitorService);
    qaTaskResultService = module.get(QaTaskResultService);
  });

  const clearTestData = async () => {
    const batchEntities = await entityManager.find(BatchEntity, {
      where: {
        orgId: testUser.currentOrg,
      },
    });
    await Bluebird.map(batchEntities, async (batch) => {
      await entityManager.delete(BatchDiligenceEntity, { batchId: batch.batchId });
    });
    await entityManager.delete(BatchEntity, { orgId: testUser.currentOrg });
    await entityManager.delete(DiligenceHistoryEntity, { orgId: testUser.currentOrg });
    const taskEntities = await entityManager.find(QaTaskEntity, {
      where: {
        orgId: testUser.currentOrg,
      },
    });
    await Bluebird.map(taskEntities, async (task) => {
      await entityManager.delete(QaTaskResultAnalyzedEntity, { taskId: task.id });
      await entityManager.delete(QaTaskResultEntity, { taskId: task.id });
    });

    const datasetEntities = await entityManager.find(QaDatasetEntity, {
      where: {
        orgId: testUser.currentOrg,
      },
    });
    await Bluebird.map(datasetEntities, async (dataset) => {
      await entityManager.delete(QaDatasetItemEntity, { datasetId: dataset.id });
    });
    await entityManager.delete(QaDatasetEntity, { orgId: testUser.currentOrg });
  };

  beforeAll(async () => {});

  it('创建整个流程需要的数据并执行分析', async () => {
    await clearTestData();
    // job#0
    const modelName = '监控模型-QA-测试-' + Date.now();
    const baseModel: RiskModelEntity = await modelInitMonitorService.createMonitorModel(
      testUser,
      modelName,
      testUser.currentOrg,
      RiskModelTypeEnums.MonitorModel,
    );

    console.log('BASE_MODEL_ID', baseModel.modelId);

    jest.spyOn(snapshotService.snapshotBatchQueue, 'sendMessageV2').mockImplementation((msg) => {
      console.log('SNAPSHOT_MSG', msg);
      return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.BatchDiligence);
    });

    // 执行监控
    const baseBatchEntity = await doMonitor(
      qaCompanies, // 监控企业
      baseModel.modelId, // 监控模型
      testUser,
    );
    console.log('BASE_BATCH_ID', baseBatchEntity.batchId);

    // job#1
    const qaCompanyItems: CreateQaCompayResponseItem[] = await qaComapnyService.createCompanyInfoBatch(
      qaCompanies.map((m) => m.companyId),
      testUser,
      false,
    );
    // 创建测试数据集
    const dataset = await datasetService.createSet(
      {
        name: 'test dataset:' + new Date().toISOString(),
        qaType: QaTypeEnums.Monitor,
        comment: 'test dataset',
      },
      testUser,
    );

    await datasetService.addItemsToSet(
      {
        datasetId: dataset.id,
        itemIds: qaCompanyItems.map((m) => m.entity?.companyIntId),
      },
      testUser,
    );

    console.log('DATASET_ID', dataset.id);

    const riskModelId = baseModel.modelId; // 从 job#0 获取
    const diligenceBatchId = baseBatchEntity.batchId; // 从 job#2 获取
    const datasetId = dataset.id; // fake
    const taskEntity = await qaTaskService.createTask(
      {
        taskName: 'test task' + Date.now(),
        datasetId,
        taskType: QaTaskTypeEnums.Test,
        baseModelId: riskModelId,
      },
      testUser,
    );
    await entityManager.update(QaTaskEntity, { id: taskEntity.id }, { refDiligenceBatchIdBase: diligenceBatchId });
    console.log('TASK_ID', taskEntity.id);

    const qaTaskId = taskEntity.id;
    await qaTaskService.onTaskFinished(qaTaskId, { batchId: diligenceBatchId } as BatchEntity);
  });

  describe('debug', () => {
    it('statisticsSearch() test', async () => {
      const result: PaginationResponse = await qaTaskResultService.statisticsSearch(
        {
          taskId: 8,
          modelId: 3576,
          resultGroup: QaTaskResultGroupEnums.Company,
          pageSize: 10,
          pageIndex: 1,
          sortField: 'tpCount',
          sortOrder: 'DESC',
          logicFilters: [
            {
              field: 'tpCount',
              operation: LoginFilterOperator.GreaterThan,
              value: 0,
            },
            {
              field: 'tnCount',
              operation: LoginFilterOperator.GreaterThan,
              value: 2,
            },
          ],
        },
        testUser,
      );
      expect(result.data.length).toBeGreaterThan(1);
      expect(result.data[0].tpCount).toBeGreaterThanOrEqual(result.data[1].tpCount);
    });

    it('task search', async () => {
      const res = await qaTaskService.search(
        {
          pageIndex: 1,
          pageSize: 10,
        },
        testUser,
      );
      console.log('res', res);
    });

    it('debug analyzeCompany()', async () => {
      const result = await qaTaskResultService.analyzeCompany(8, 3576);
      expect(result).toBeDefined;
    });
  });
});
