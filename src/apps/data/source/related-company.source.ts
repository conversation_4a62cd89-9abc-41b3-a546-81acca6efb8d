import { Injectable } from '@nestjs/common';
import { flatten } from 'lodash';
import * as Bluebird from 'bluebird';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { NebulaGraphHelper } from '../helper/nebula.graph.helper';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { EntStatusMap } from '../../../libs/constants/company.constants';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import { getCompareResult } from '../../../libs/utils/diligence/diligence.utils';
import { ALLRelatedType, CustomizedRelatedEnums, RelatedChangeTypeEnums, RelatedTypeMap } from '../../../libs/constants/related.constants';
import { RelatedPartRequest } from '../../../libs/model/company/RelatedPartRequest';
import { relatedTypeAnnotations, RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { RelatedPartyGroupPO } from '../../../libs/model/diligence/graph/RelatedPartyGroupPO';
import { NebulaRelatedEdgeEnums } from '../../../libs/enums/dimension/NebulaRelatedEdgeEnums';
import { DimensionFieldCompareTypeEnums } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { CompanyRelatedListParams } from '../../../libs/model/data/request/CompanyRelatedListParams';
import { paginate } from '../../../libs/utils/utils';
import { getDimesionHitStrategies } from '../../dimension/dimension.helper';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from '../../../libs/exceptions/exceptionConstants';
import { MonitorCompanyRelatedPartyEntity } from '../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { CompanySearchService } from '../../company/company-search.service';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { ConfigService } from '../../../libs/config/config.service';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { DistributedSystemResourceEntity } from '../../../libs/entities/DistributedSystemResourceEntity';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import { DistributeResourceStatusEnums } from '../../../libs/enums/DistributeResourceStatusEnums';
import { RelatedHelper } from '../helper/related.helper';
import { MonitorCompanyRelatedDailyEntity } from '../../../libs/entities/MonitorCompanyRelatedDailyEntity';
import { ProductCodeEnums } from '../../../client';
import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
import { MonitorCompanyRelatedStatusEnum } from '../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import { IAnalyzeService } from './analyze.interface';

/**
 * 关联方相关维度分析服务
 */
@Injectable()
export class RelatedCompanySource implements IAnalyzeService<HitDetailsBaseQueryParams, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(RelatedCompanySource.name);

  MaxRelatedCompanyCount = 2000;

  constructor(
    private readonly configService: ConfigService,
    private readonly nebulaGraphHelper: NebulaGraphHelper,
    private readonly companySearchService: CompanySearchService,
    @InjectRepository(MonitorCompanyRelatedPartyEntity) private readonly relatedCompanyRepo: Repository<MonitorCompanyRelatedPartyEntity>,
    @InjectRepository(MonitorCompanyRelatedDailyEntity) private readonly relatedDailyRepo: Repository<MonitorCompanyRelatedDailyEntity>,
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>,
    @InjectRepository(DistributedSystemResourceEntity) private readonly distributedSystemResourceRepo: Repository<DistributedSystemResourceEntity>,
    private readonly relatedHelper: RelatedHelper,
  ) {}

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(
      DimensionHitStrategyPOs,
      async (d: DimensionHitStrategyPO) => {
        /**
         * 处理命中描述信息需要的参数
         */
        const desData = {
          isHidden: '',
          isHiddenY: '',
        };
        let hitCount = 0;

        if (DimensionTypeEnums.RelatedCompanyChange === d.key) {
          // RelatedCompanyChange 只对监控企业 监控任务时生效
          if (d?.dimensionFilter && d?.dimensionFilter?.monitorGroupId && d?.dimensionFilter?.isRelated == false) {
            const dimensionResponse = await this.getRelatedPartyHitDetail(d, companyId);
            // 关联方变更维度 需要将本次取到的关联方更上次的关联方作比较，返回新增或者减少的关联方
            let relatedChange = [];
            const changeTypeField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedChangeType);
            const changeType = changeTypeField?.fieldValue[0] || RelatedChangeTypeEnums.Added;
            if (changeType === RelatedChangeTypeEnums.Removed) {
              relatedChange = await this.getRelatedRemoved(d.dimensionFilter.monitorGroupId, companyId, dimensionResponse);
            } else {
              relatedChange = await this.getRelatedAdded(d.dimensionFilter.monitorGroupId, companyId, dimensionResponse);
            }
            hitCount = relatedChange.length;
          }
        } else {
          const dimensionResponse = await this.getRelatedPartyHitDetail(d, companyId);
          hitCount = dimensionResponse?.length;
        }

        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hitCount = 0;
        }
        if (hitCount > 0) {
          return processDimHitResPO(d, hitCount, desData);
        }
        return null;
      },
      { concurrency: 5 },
    ).then((item) => item.filter((t) => t));
  }

  /**
   * 保存企业前x个关联方id日志，用于后续判断关联方变更
   * @param orgId
   * @param monitorGroupId
   * @param companyIdPrimary
   */
  async saveCompanyRelatedDaily(orgId: number, monitorGroupId: number, companyIdPrimary: string) {
    try {
      const relatedCompany = await this.getCompanyRelatedList(
        {
          companyId: companyIdPrimary,
          monitorGroupId,
        },
        orgId,
        ProductCodeEnums.Pro,
        false,
      );

      let relatedCompanies = relatedCompany.Result;
      if (relatedCompany?.Result?.length > this.MaxRelatedCompanyCount) {
        relatedCompanies = relatedCompany.Result.slice(0, this.MaxRelatedCompanyCount);
      }
      // 保存当前关联方列表到每日记录
      await this.relatedDailyRepo.save({
        orgId,
        companyId: companyIdPrimary,
        monitorGroupId,
        relatedIds: relatedCompanies.map((r) => r.companyKeynoRelated)?.join(',') || '',
        updateTime: new Date(),
        product: ProductCodeEnums.Pro,
      });
      // TODO: 删除历史的关联方每日记录
    } catch (error) {
      this.logger.error(`保存企业关联方id日志失败，企业ID：${companyIdPrimary}，错误信息：${error.message}`);
      throw error;
    }
  }

  /**
   * 将本次取到的关联方更上次的关联方作比较，返回新增的关联方
   * @param modelId
   * @param companyId
   * @param currentRelatedIds 当前查询到的关联方ID数组
   * @returns
   * - addedRelated 新增符合关联方定义的企业
   */
  private async getRelatedAdded(monitorGroupId: number, companyId: string, dimensionResponse: RelatedPartyGroupPO[]): Promise<RelatedPartyGroupPO[]> {
    // 关联方变更维度 需要将本次取到的关联方更上次的关联方作比较，返回新增或者减少的关联方
    const relatedCompany = await this.relatedDailyRepo.findOne({
      where: {
        companyId,
        monitorGroupId,
      },
      order: {
        id: 'DESC',
      },
    });

    // 本次新增的关联方
    let addedRelated: RelatedPartyGroupPO[] = [];
    // 只对有过历史记录的比较差异
    if (relatedCompany) {
      // 比较 relatedCompany.relatedIds 和 dimensionResponse中的companyKeynoRelated的查询，计算新增的关联方和减少的关联方，并将新增的关联方和减少的关联方返回, 同时将dimensionResponse的关联方保存到 MonitorCompanyRelatedDailyEntity 中

      // 获取上次保存的关联方ID列表
      const previousRelatedIds = relatedCompany?.relatedIds?.split(',') || [];

      // 获取当前查询到的关联方ID列表
      const currentRelatedIds = dimensionResponse.map((r) => r.companyKeynoRelated);
      // 计算新增的关联方ID (存在于当前但不在上次)
      const addedIds = currentRelatedIds?.filter((id) => !previousRelatedIds?.includes(id));

      if (addedIds.length > 0) {
        addedRelated = dimensionResponse.filter((r) => addedIds.includes(r.companyKeynoRelated));
      }
    }
    return addedRelated;
  }

  /**
   * 将本次取到的关联方更上次的关联方作比较，返回失效的关联方
   * @param modelId
   * @param companyId
   * @param currentRelatedIds 当前查询到的关联方ID数组
   * @returns
   * - existRelatedRemoved 已监控关联方中不再符合关联方定义的企业
   */

  private async getRelatedRemoved(monitorGroupId: number, companyId: string, dimensionResponse: RelatedPartyGroupPO[]): Promise<RelatedPartyGroupPO[]> {
    // 关联方变更维度 需要将本次取到的关联方更上次的关联方作比较，返回新增或者减少的关联方
    const relatedCompany = await this.relatedDailyRepo.findOne({
      where: {
        companyId,
        monitorGroupId,
      },
      order: {
        id: 'DESC',
      },
    });

    // 已监控关联方中不在是关联方的企业
    let existRelatedRemoved = [];
    // 只对有过历史记录的比较差异
    if (relatedCompany) {
      // 比较 relatedCompany.relatedIds 和 dimensionResponse中的companyKeynoRelated的查询，计算新增的关联方和减少的关联方，并将新增的关联方和减少的关联方返回, 同时将dimensionResponse的关联方保存到 MonitorCompanyRelatedDailyEntity 中

      // 获取上次保存的关联方ID列表
      const previousRelatedIds = relatedCompany?.relatedIds?.split(',') || [];

      // 获取当前查询到的关联方ID列表
      const currentRelatedIds = dimensionResponse.map((r) => r.companyKeynoRelated);
      // 计算减少的关联方ID (存在于上次但不在当前)
      const removedIds = previousRelatedIds?.filter((id) => !currentRelatedIds?.includes(id));
      if (removedIds.length > 0) {
        existRelatedRemoved = await this.relatedCompanyRepo.find({
          select: ['companyIdRelated', 'relatedTypeStr'],
          // 从数据库中查询已存在的关联方
          where: {
            companyIdPrimary: companyId,
            monitorGroupId,
            status: MonitorCompanyRelatedStatusEnum.Valid,
            companyIdRelated: In(removedIds),
          },
        });

        const monitorRelatedCompany = await this.relatedCompanyRepo.manager.find(MonitorCompanyEntity, {
          monitorGroupId,
          companyId: In(removedIds),
        });
        existRelatedRemoved = existRelatedRemoved.map((r) => {
          const company = monitorRelatedCompany.find((c) => c.companyId === r.companyIdRelated);
          const relatedTypeDescList = r.relatedTypeStr.split(',').map((t) => relatedTypeAnnotations[t]);
          return {
            companyKeynoRelated: r.companyIdRelated,
            companyNameRelated: company?.companyName,
            relatedTypes: r.relatedTypeStr.split(','),
            relatedTypeDescList,
            // 标记为减少的关联方
            removed: true,
          };
        });
      }
    }
    return existRelatedRemoved;
  }

  // /**
  //  * 新增关联方中如果已经监控，且状态是无效，需要变更成有效，并更新relatedType
  //  * @param addedRelated
  //  * @param monitorGroupId
  //  * @param companyId
  //  */
  // private async updateRelatedToValid(addedRelated: RelatedPartyGroupPO[], monitorGroupId: number, companyId: string) {
  //   const needValidateRelated = await this.relatedCompanyRepo.find({
  //     select: ['companyIdRelated', 'relatedTypeStr'],
  //     // 从数据库中查询已存在的关联方
  //     where: {
  //       companyIdPrimary: companyId,
  //       monitorGroupId,
  //       status: MonitorCompanyRelatedStatusEnum.Invalid,
  //       companyIdRelated: In(addedRelated.map((r) => r.companyKeynoRelated)),
  //     },
  //   });
  //   // 新增的关联方，当前已监控且状态是无效，需要变更成有效，并更新relatedType
  //   if (needValidateRelated.length > 0) {
  //     await Bluebird.map(needValidateRelated, async (company) => {
  //       const relatedCompany = addedRelated.find((related) => related.companyKeynoRelated == company.companyIdRelated);
  //       const relatedTypeStr = relatedCompany.relatedTypes.join(',');
  //       await this.relatedCompanyRepo.update(
  //         { id: company.id, status: MonitorCompanyRelatedStatusEnum.Invalid },
  //         {
  //           status: MonitorCompanyRelatedStatusEnum.Valid,
  //           relatedTypeStr,
  //         },
  //       );
  //     });
  //   }
  // }

  // /**
  //  * 已监控关联方中不再符合关联方定义的企业，需要变更成无效
  //  * @param existRelatedRemoved
  //  * @param monitorGroupId
  //  * @param companyId
  //  */
  // private async updateRelatedToInvalid(existRelatedRemoved: RelatedPartyGroupPO[], monitorGroupId: number, companyId: string) {
  //   if (existRelatedRemoved.length > 0) {
  //     // 当前监控关联方是有效，需要变更成失效
  //     await this.relatedCompanyRepo.update(
  //       {
  //         monitorGroupId,
  //         companyIdPrimary: companyId,
  //         companyIdRelated: In(existRelatedRemoved.map((r) => r.companyKeynoRelated)),
  //         status: MonitorCompanyRelatedStatusEnum.Valid,
  //       },
  //       { status: MonitorCompanyRelatedStatusEnum.Invalid },
  //     );
  //   }
  // }

  async getDimensionDetail(dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    let dimensionDetails = HitDetailsBaseResponse.ok();
    try {
      const { keyNo, pageIndex, pageSize } = params;
      let dimensionResponse = await this.getRelatedPartyHitDetail(dimension, keyNo);

      let hitCount = dimensionResponse.length;

      // const { monitorGroupId, isRelated } = dimension.dimensionFilter;
      // if (monitorGroupId && !isRelated && DimensionTypeEnums.RelatedCompanyChange === dimension.key) {
      if (DimensionTypeEnums.RelatedCompanyChange === dimension.key && dimension?.dimensionFilter) {
        const { monitorGroupId, isRelated } = dimension?.dimensionFilter;
        if (monitorGroupId && isRelated == false) {
          // 关联方变更维度 需要将本次取到的关联方更上次的关联方作比较，返回新增或者减少的关联方
          let relatedChange = [];
          const changeTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedChangeType);
          const changeType = changeTypeField?.fieldValue[0] || RelatedChangeTypeEnums.Added;
          if (changeType === RelatedChangeTypeEnums.Removed) {
            relatedChange = await this.getRelatedRemoved(monitorGroupId, keyNo, dimensionResponse);
          } else {
            relatedChange = await this.getRelatedAdded(monitorGroupId, keyNo, dimensionResponse);
          }
          hitCount = relatedChange.length;
          if (hitCount > 0) {
            dimensionResponse = relatedChange;
          }
        }
      }

      if (!hitCount) {
        return dimensionDetails;
      }
      const hitCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
        // 不满足 命中记录条数规则 标记未命中
        return dimensionDetails;
      }
      const start = pageSize * (pageIndex - 1);
      const sliceList = dimensionResponse?.slice(start, start + pageSize);
      //查询关联方企业地址
      const relatedTypes = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType)?.fieldValue;
      if (relatedTypes?.includes(NebulaRelatedEdgeEnums.HasAddress)) {
        const companyMap = await this.nebulaGraphHelper.getCompanyInfoMap(sliceList.map((s) => s.companyKeynoRelated));
        sliceList.forEach((s) => {
          if (s.relatedTypes?.length && s.relatedTypes.includes(NebulaRelatedEdgeEnums.HasAddress)) {
            s.contactList = companyMap[s.companyKeynoRelated]?.address ? [companyMap[s.companyKeynoRelated].address] : [];
          }
        });
      }
      return Object.assign(dimensionDetails, {
        Paging: {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: dimensionResponse?.length || 0,
        },
        Result: sliceList,
      });
    } catch (error) {
      this.logger.error(`rover graph getDimensionDetail err: ${error},dimension: ${dimension?.dimensionDef.key}`);
      dimensionDetails = HitDetailsBaseResponse.failed(error.response?.error || error.message, DimensionSourceEnums.EnterpriseLib, error.response?.code);
    }
    return dimensionDetails;
  }

  /**
   * 获取关联方列表（最多只会返回前 MaxRelatedCompanyCount 条）
   * @param params
   * @param currentUser
   * @param usePage true-使用分页
   * @returns
   */
  public async getCompanyRelatedList(
    params: CompanyRelatedListParams,
    orgId: number,
    product: ProductCodeEnums,
    usePage = true,
  ): Promise<HitDetailsBaseResponse> {
    const { companyId, keyword, monitorGroupId } = params;
    // const { currentOrg: orgId, currentProduct: product } = currentUser;

    // 1 根据监控模型 monitorModelId 查询监控中关联方的设置，如果未开启直接返回空
    const group = await this.monitorGroupRepo.findOne({ monitorGroupId, orgId });
    if (!group) {
      throw new BadParamsException(RoverExceptions.Monitor.Group.NotFound);
    }
    const monitorModelId = group.monitorModelId;
    // 检查是否能使用指定的模型
    if (monitorModelId) {
      const distributedSystemResource = await this.distributedSystemResourceRepo.findOne({
        where: {
          orgId,
          resourceType: RiskModelTypeEnums.MonitorModel,
          product,
          resourceId: monitorModelId,
        },
      });
      if (!distributedSystemResource) {
        throw new BadParamsException(RoverExceptions.RiskModel.NotFound);
      }
      if (distributedSystemResource.distributeStatus === DistributeResourceStatusEnums.Deprecated) {
        throw new BadParamsException(RoverExceptions.RiskModel.Deprecated);
      }
    }

    const dimHitStrategies = await getDimesionHitStrategies(group.monitorModelId, [DimensionTypeEnums.RelatedCompanyChange], this.relatedCompanyRepo.manager);

    // 2 根据模型中关联方的设置查询关联方列表
    let relatedCompanyList = await this.getRelatedPartyHitDetail(dimHitStrategies[0], companyId);
    relatedCompanyList = relatedCompanyList.map((r) => {
      //  统一枚举：edgeType只在掉图谱接口用，别的地方都用value
      r.relatedTypes = r.relatedTypes?.map((t) => ALLRelatedType.find((a) => a.edgeType === t)?.value || t);
      return r;
    });

    // 3 对 relatedCompanyList 使用关键词过滤、排序、分页
    if (keyword) {
      // 使用关键词对companyNameRelated过滤
      relatedCompanyList = relatedCompanyList.filter((item) => {
        return item.companyNameRelated.toLowerCase().includes(keyword.toLowerCase());
      });
    }

    // 排序
    relatedCompanyList = relatedCompanyList.sort((a, b) => {
      return a.companyNameRelated.localeCompare(b.companyNameRelated);
    });

    // 分批查询关联方列表中已经被加入关联方的企业打上标签
    const batchSize = 500; // 每批次查询的最大条数
    let offset = 0; // 当前查询的起始位置
    let monitored = []; // 存储所有查询结果

    do {
      // 获取当前批次的 companyKeynoRelated 列表
      const batchCompanyKeys = relatedCompanyList.slice(offset, offset + batchSize).map((x) => x.companyKeynoRelated);

      // 1.查询当前批次的已监控的数据
      const batchResults = await this.relatedCompanyRepo.find({
        where: {
          monitorGroupId,
          companyIdRelated: In(batchCompanyKeys),
          companyIdPrimary: companyId,
        },
      });

      // 将当前批次的结果合并到总结果中
      monitored = monitored.concat(batchResults);
      // 更新 offset，准备查询下一批次
      offset += batchSize;
    } while (offset < relatedCompanyList.length); // 如果还有数据，继续循环

    // 分页
    const PageIndex = params?.pageIndex || 1;
    const PageSize = params?.pageSize || 10;
    const data = usePage ? paginate(relatedCompanyList, PageIndex, PageSize) : relatedCompanyList;

    data.forEach((x) => {
      x['isMonitor'] = !!monitored.find((m) => m.companyIdRelated === x.companyKeynoRelated);
    });
    return Object.assign(new HitDetailsBaseResponse(), {
      Paging: {
        PageSize,
        PageIndex,
        TotalRecords: relatedCompanyList.length || 0,
      },
      Result: data,
    });
  }

  /**
   * 根据数据维度设置获取关联方列表，只会取前 MaxRelatedCompanyCount 条数据在过滤各种属性
   * @param dimension
   * @param companyId
   * @returns
   */
  private async getRelatedPartyHitDetail(dimension: DimensionHitStrategyPO, companyId: string): Promise<RelatedPartyGroupPO[]> {
    const customizedRelated = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.customizedRelated)?.fieldValue;
    let response: RelatedPartyGroupPO[] = [];
    if (customizedRelated?.[0] == CustomizedRelatedEnums.IcbcSFRelated) {
      // SF 定制关联方 只支持港澳台企业过滤
      response = await this.relatedHelper.getIcbcSFRelateds(companyId);
    } else {
      const requestParam = new RelatedPartRequest();
      requestParam.companyIds = [companyId];
      const sourcePath = dimension?.dimensionDef?.detailSourcePath ? dimension.dimensionDef?.detailSourcePath : dimension.dimensionDef?.sourcePath;
      dimension.strategyFields.forEach((field) => {
        switch (field.dimensionFieldKey) {
          case DimensionFieldKeyEnums.cycle: {
            const cycle = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cycle)?.fieldValue[0];
            if (cycle) {
              requestParam.cycle = cycle;
            }
            break;
          }
          case DimensionFieldKeyEnums.isValid: {
            const isValid = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid)?.fieldValue[0] ?? -1;
            if (isValid >= 0) {
              requestParam.dataStatus = isValid;
            }
            break;
          }
          case DimensionFieldKeyEnums.relatedRoleType: {
            let fieldValues = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType)?.fieldValue;
            //关联方类型,必传
            if (!fieldValues?.length) {
              throw new Error('关联方类型必传');
            }
            if (fieldValues.includes(RelatedTypeEnums.RelatedMember)) {
              fieldValues = RelatedTypeMap.map((r) => r.value).filter((v) => v !== RelatedTypeEnums.RelatedMember);
              //股比默认50%
              requestParam.percentage = 50;
            }
            const relatedTypeToEdgeTypeMap = ALLRelatedType.reduce((acc, { value, edgeType }) => {
              acc[value.toString()] = edgeType;
              return acc;
            }, {});
            const edgeTypes = fieldValues.map((v) => relatedTypeToEdgeTypeMap[v.toString()]);
            if (!edgeTypes.length) {
              throw new Error('关联方类型必传');
            }
            requestParam.relatedTypes = edgeTypes;
            break;
          }
        }
      });
      switch (dimension.key) {
        case DimensionTypeEnums.BusinessAnomalies:
        case DimensionTypeEnums.SeriousViolation:
        case DimensionTypeEnums.BusinessAnomaliesWithSamePhoneAndAddress:
        case DimensionTypeEnums.MoneyLaundering: {
          const dimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
          if (dimensionField) {
            const caseReasonTypes = flatten(dimensionField.fieldValue);
            const caseReasonCompareType = dimensionField.compareType;
            if (caseReasonTypes?.length) {
              switch (caseReasonCompareType) {
                case DimensionFieldCompareTypeEnums.ContainsAny: {
                  requestParam.reasonTypes = caseReasonTypes;
                  break;
                }
                case DimensionFieldCompareTypeEnums.ExceptAny: {
                  requestParam.excludedReasonTypes = caseReasonTypes;
                  break;
                }
                default:
                  break;
              }
            }
          }
          //被告 关联方成员企业有裁判文书信息,默认查被告 0：原告 1：被告 2：第三人 3：其他
          const judgementRole = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judgementRole)?.fieldValue?.[0];
          if (judgementRole >= 0) {
            requestParam.judgementRole = judgementRole;
          }
          const caseTypes = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType)?.fieldValue;
          if (caseTypes?.length) {
            //案件类型支持：ms，zx，xs，bq，pc，xz，gx，other，zc，qsx，gsx
            requestParam.caseTypes = caseTypes;
          }
          //风险类型
          const relatedRiskTypes = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRiskType)?.fieldValue;
          if (relatedRiskTypes?.length) {
            requestParam.riskTypes = relatedRiskTypes;
          } else {
            return [];
          }
          break;
        }
        case DimensionTypeEnums.RelatedAnnouncement: {
          //被告 关联方成员企业有开庭公告信息,默认查被告 0：原告 1：被告 2：第三人 3：其他
          const courtType = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.courtType)?.fieldValue?.[0] ?? 1;
          if (courtType >= 0) {
            requestParam.announcementRole = courtType;
          }
          // //股比
          // const percentage = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.investRatio)?.fieldValue?.[0] ?? 50;
          // if (percentage >= 0) {
          //   requestParam.percentage = percentage;
          // }
          //风险类型:开庭公告
          const relatedRiskType = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRiskType)?.fieldValue ?? ['Announcement'];
          if (relatedRiskType?.length) {
            requestParam.riskTypes = relatedRiskType;
          } else {
            return [];
          }
          break;
        }
        case DimensionTypeEnums.RelatedCompanyChange:
        case DimensionTypeEnums.RelatedCompanies: {
          //企业状态
          const shortStatusList = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyStatus)?.fieldValue;
          if (shortStatusList?.length) {
            //将code值转换为对应的中文名称
            requestParam.shortStatusList = shortStatusList.map((s) => EntStatusMap[s]);
          }
          break;
        }
        default: {
          return [];
        }
      }
      response = await this.nebulaGraphHelper.getCompanyRelatedParties(requestParam, sourcePath);
    }

    // let response: RelatedPartyGroupPO[] = await this.nebulaGraphHelper.getCompanyRelatedParties(requestParam, sourcePath);
    if (!response?.length) {
      return [];
    }

    // 取关联方上限值的的1.5倍结果集，后续再根据各种条件过滤
    if (response.length > this.MaxRelatedCompanyCount * 1.5) {
      response = response.slice(0, this.MaxRelatedCompanyCount * 1.5);
    }
    // const flagCodes = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyFlag)?.fieldValue;
    // if (flagCodes?.length) {
    //   //过滤分支机构为持牌机构的企业数据
    //   const fncCompanyIds = await this.nebulaGraphHelper.searchFNCCompanyIds(flagCodes);
    //   response = response.filter((r) => !fncCompanyIds.includes(r.companyKeynoRelated));
    // }

    const filterCompanyFlag = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFilterRelatedCompany)?.fieldValue;

    if (filterCompanyFlag?.length && filterCompanyFlag?.[0] === 1 && response.length) {
      // 分批查询关联方列表中需要过滤的港澳台企业
      const batchSize = 500; // 每批次查询的最大条数
      let offset = 0; // 当前查询的起始位置
      let unsupportedCompanies = [];

      do {
        const batchCompanyKeys = response.slice(offset, offset + batchSize).map((x) => x.companyKeynoRelated);
        //查询当前批次不支持的企业数据
        if (batchCompanyKeys.length) {
          const { unsupportedCompanies: batchUnsupportedCompanies } = await this.companySearchService.checkCompanyStandardCode(batchCompanyKeys);
          unsupportedCompanies = unsupportedCompanies.concat(batchUnsupportedCompanies);
        }

        // 更新 offset，准备查询下一批次
        offset += batchSize;
      } while (offset < response.length); // 如果还有数据，继续循环
      if (response.length && unsupportedCompanies.length) {
        return response.filter((x) => !unsupportedCompanies.some((y) => y.id === x.companyKeynoRelated));
      }
    }

    // //成立日期 暂时不支持开放
    // const companyStartDateStrategyField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CompanyStartDate);
    // if (companyStartDateStrategyField) {
    //   //timeValue 是数字，比如设置为 1，根据 option中的 unit 单位是天，且 compareType 是 LessThanOrEqual，则表示成立日期小于当前日期1天
    //   const timeValue = companyStartDateStrategyField.fieldValue[0];
    //   const unit = companyStartDateStrategyField.options[0].unit;
    //   const compareType = companyStartDateStrategyField.compareType;
    //   //根据 compareType 以及当前日期计算出目标日期时间戳（单位毫秒）
    //   const targetDate = Math.floor(getTargetDate(timeValue, unit) / 1000);
    //   response = response?.filter((x) => {
    //     // if (!x.businessStartTime) return false;
    //     if (x.businessStartTime === undefined || x.businessStartTime === null) return false;
    //     return getCompareResult(targetDate, +x.businessStartTime, compareType);
    //   });
    // }
    // //实缴资本 暂时不支持开放
    // const paidCapitalStrategyField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.realRegistrationAmount);
    // if (paidCapitalStrategyField) {
    //   const paidCapital = paidCapitalStrategyField.fieldValue[0];
    //   const compareType = paidCapitalStrategyField.compareType;
    //   response = response?.filter((x) => {
    //     // if (!x.paidCapi) return false;
    //     if (x.paidCapi === undefined || x.paidCapi === null) return false;
    //     const normalizedCapital = normalizeCapitalToWanYuan(x.paidCapi);
    //     if (normalizedCapital === null) return false;
    //     return getCompareResult(normalizedCapital, paidCapital, compareType);
    //   });
    // }

    // 截取前 MaxRelatedCompanyCount 条数据
    response = response.slice(0, this.MaxRelatedCompanyCount);

    // 排序，防止快速点击出现实时查询导致的顺序错乱的问题
    response?.sort((a, b) => {
      return a.companyNameRelated.localeCompare(b.companyNameRelated);
    });
    return response;
  }
}
