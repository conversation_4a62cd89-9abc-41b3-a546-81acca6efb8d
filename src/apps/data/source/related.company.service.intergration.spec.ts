import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { AppTestModule } from '../../app/app.test.module';
import { DataModule } from '../data.module';
import { RelatedCompanySource } from './related-company.source';
import { ProductCodeEnums } from '../../../client';
import { getDimensionHitStrategyPO } from '../../test_utils_module/dimension.test.utils';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { CustomizedRelatedEnums, RelatedChangeTypeEnums } from '../../../libs/constants/related.constants';
import { DimensionFieldCompareTypeEnums } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { isFilterRelatedCompanyMap } from '../../../libs/constants/risk.change.constants';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
import { clearMonitorTestData } from '../../test_utils_module/monitor.test.tools';
import { MonitorCompanyRelatedDailyEntity } from '../../../libs/entities/MonitorCompanyRelatedDailyEntity';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { MonitorCompanyRelatedStatusEnum } from '../../../libs/enums/monitor/MonitorCompanyStatusEnums';

// 生成测试用户ID
const [testOrgId, testUserId] = generateUniqueTestIds('related.company.service.intergration.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(120 * 1000);

describe('test risk change service', () => {
  let nebulaGraphService: RelatedCompanySource;
  let entityManager: EntityManager;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    nebulaGraphService = module.get<RelatedCompanySource>(RelatedCompanySource);
    entityManager = getManager();
  });
  afterAll(async () => {
    // await clearMonitorTestData(entityManager, testUser);
    // await entityManager.connection.close();
  });

  it.skip('debug-icbc-sf查询企业关联方', async () => {
    const result = await nebulaGraphService.getCompanyRelatedList(
      {
        pageSize: 5,
        pageIndex: 1,
        companyId: '1990aed8096105169d5d36193eab4e79',
        monitorGroupId: 2858,
      },
      1003542,
      ProductCodeEnums.Pro,
    );

    expect(result).toBeDefined();
  });

  it('icbc-sf监控关联方变更', async () => {
    await clearMonitorTestData(entityManager, testUser);
    try {
      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanyChange, [
        {
          fieldKey: DimensionFieldKeyEnums.customizedRelated,
          fieldValue: [CustomizedRelatedEnums.IcbcSFRelated],
          options: [
            // { label: '汇添富定义关联方(Nebula查询)', value: CustomizedRelatedEnums.HtfRelated },
            { label: 'IcbcSF关联方(独立逻辑)', value: CustomizedRelatedEnums.IcbcSFRelated },
          ],
          compareType: DimensionFieldCompareTypeEnums.Equal,
          accessScope: 1,
        },
        {
          fieldKey: DimensionFieldKeyEnums.isFilterRelatedCompany,
          fieldValue: [1],
          options: isFilterRelatedCompanyMap,
          compareType: DimensionFieldCompareTypeEnums.Equal,
          accessScope: 1,
        },
        {
          fieldKey: DimensionFieldKeyEnums.relatedChangeType,
          fieldValue: [RelatedChangeTypeEnums.Removed],
          compareType: DimensionFieldCompareTypeEnums.Equal,
          accessScope: 1,
        },
      ]);

      const group = await entityManager.save(MonitorGroupEntity, {
        name: '默认分组',
        orgId: testUser.currentOrg,
        product: testUser.currentProduct,
        comment: '初始化默认分组',
        monitorModelId: testUser.currentOrg + 1111,
        ownerId: testUser.userId,
        updateBy: testUser.userId,
      });

      const companyId = 'acfb204d6fbe49198a12c8279e7b2d20';
      const companyName = '永赢金融租赁有限公司';
      const relateds = [
        { companyId: '4e3587df894bcdea475124d0eeacb230', companyName: '上海永赢沪畅翔一号飞机租赁有限公司' },
        { companyId: '8ca43ae432dd1ecc64f9f47b2325833e', companyName: '上海永赢沪畅翔二号飞机租赁有限公司' },
        { companyId: 'f625a5b661058ba5082ca508f99ffe1b', companyName: '企查查科技股份有限公司' },
      ];

      await entityManager.save(MonitorCompanyEntity, [
        {
          monitorGroupId: group.monitorGroupId,
          orgId: testUser.currentOrg,
          companyId,
          companyName,
          primaryObject: 1,
          createBy: testUser.userId,
          product: testUser.currentProduct,
        },
        ...relateds.map((item) => ({
          monitorGroupId: group.monitorGroupId,
          orgId: testUser.currentOrg,
          companyId: item.companyId,
          companyName: item.companyName,
          primaryObject: 0,
          createBy: testUser.userId,
          product: testUser.currentProduct,
        })),
      ]);

      await entityManager.save(
        MonitorCompanyRelatedPartyEntity,
        relateds.map((item) => ({
          monitorGroupId: group.monitorGroupId,
          companyIdPrimary: companyId,
          companyIdRelated: item.companyId,
          companyNameRelated: item.companyName,
          orgId: testUser.currentOrg,
          relatedTypeStr: 'ControlCompany',
          product: testUser.currentProduct,
          status: MonitorCompanyRelatedStatusEnum.Valid,
        })),
      );

      await entityManager.save(MonitorCompanyRelatedDailyEntity, {
        orgId: testUser.currentOrg,
        companyId,
        monitorGroupId: group.monitorGroupId,
        relatedIds: relateds.map((item) => item.companyId).join(','),
        updateTime: new Date(),
        product: testUser.currentProduct,
      });

      dimension.dimensionFilter = {
        startTime: 1704038400,
        endTime: 1735660799,
        monitorGroupId: group.monitorGroupId,
        isRelated: false,
      };

      const detail = await nebulaGraphService.analyze(companyId, [dimension]);
      expect(detail[0]?.totalHits).toBeGreaterThan(1);
      const result = await nebulaGraphService.getDimensionDetail(
        dimension,
        Object.assign(
          new HitDetailsBaseQueryParams(),
          {
            keyNo: companyId,
            pageIndex: 1,
            pageSize: 20,
            orgId: testUser.currentOrg,
          },
          { keyNo: companyId, companyName },
        ),
      );
      expect(result).not.toBeNull();
      expect(result.Paging.TotalRecords).toEqual(detail[0]?.totalHits);
      // const existRelatedRemoved = result.Result.find((item) => item.removed == true);
      // expect(existRelatedRemoved.companyKeynoRelated).toEqual('f625a5b661058ba5082ca508f99ffe1b');
    } catch (error) {
      await clearMonitorTestData(entityManager, testUser);
    }
  });
});
