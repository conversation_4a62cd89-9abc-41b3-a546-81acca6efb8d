import { QccLogger } from '@kezhaozhao/qcc-logger';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { Injectable } from '@nestjs/common';
import * as Bluebird from 'bluebird';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response/HitDetailsBaseResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { cloneDeep } from 'lodash';
import { Logger } from 'log4js';
import { CategoryHandlerFactory } from './category-handler/category-handler.factory';

@Injectable()
export class DimensionHitDetailProcessorV2 {
  private readonly logger: Logger = QccLogger.getLogger(DimensionHitDetailProcessorV2.name);
  searchEs: (body: any, companyId: string) => Promise<any>;
  constructor(private readonly categoryHandler: CategoryHandlerFactory) {}

  bindRiskChangeEsSearchFn(esSearchFn: (body: any, companyId: string) => Promise<any>) {
    this.searchEs = esSearchFn;
  }
  /**
   * 针对每条风险动态详情再做分析判断是否命中
   * @param detailResp 动态详情
   * @param dimension  维度定义
   * @param params
   * @returns
   */
  @TraceLog({ throwError: true, spanType: 3, spanName: 'detailAnalyze' })
  async fetchHits(detailResp: HitDetailsBaseResponse, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<any[]> {
    const hitData = [];
    await Bluebird.map(detailResp.Result, async (itemRaw) => {
      try {
        const newItem = cloneDeep(itemRaw);
        // 先对item数据中的json字段做预处理
        // 先对item数据中的json字段做预处理
        Object.keys(newItem).forEach((key) => {
          if (['Extend1', 'ChangeExtend'].includes(key)) {
            const value = newItem[key];
            try {
              newItem[key] = value ? JSON.parse(value) : {};
            } catch (error) {
              newItem[key] = value;
            }
          }
        });
        const isHit = await this.categoryHandler.process(newItem, dimension, params);
        if (isHit) {
          hitData.push(itemRaw);
        }
      } catch (e) {
        this.logger.error(`RiskChange getDimensionDetail request: ${JSON.stringify(itemRaw)}`, e);
      }
    });
    return hitData;
  }
}
