import { Injectable } from '@nestjs/common';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';

import { AbstractCategoryHandler } from './abstract-category.handler';
import { Category44Handler } from './category-44.handler';
import { Category46Handler } from './category-46.handler';
import { Category68And204Handler } from './category-68-204.handler';
import { Category72Handler } from './category-72.handler';
import { Category17And203Handler } from './category-17-203.handler';
import { Category37Handler } from './category-37.handler';
import { CategoryJudicialHandler } from './category-judicial.handler';
import { Category107Handler } from './category-107.handler';
import { CategoryEquityHandler } from './category-equity.handler';
import { Category114Handler } from './category-114.handler';
import { Category123Handler } from './category-123.handler';
import { CategorySimpleHandler } from './category-simple.handler';

/**
 * Category Handler 工厂类
 *
 * 负责管理所有的 category handler
 * 提供统一的入口来处理不同类别的风险变更数据
 * 所有handler通过NestJS依赖注入管理
 */
@Injectable()
export class CategoryHandlerFactory {
  private handlers: AbstractCategoryHandler[] = [];
  private readonly categoryHandlerMap: Map<RiskChangeCategoryEnum, AbstractCategoryHandler> = new Map();

  constructor(
    // 通过依赖注入获取所有handler实例
    private readonly category44Handler: Category44Handler,
    private readonly category46Handler: Category46Handler,
    private readonly category68And204Handler: Category68And204Handler,
    private readonly category72Handler: Category72Handler,
    private readonly category17And203Handler: Category17And203Handler,
    private readonly category37Handler: Category37Handler,
    private readonly categoryJudicialHandler: CategoryJudicialHandler,
    private readonly category107Handler: Category107Handler,
    private readonly categoryEquityHandler: CategoryEquityHandler,
    private readonly category114Handler: Category114Handler,
    private readonly category123Handler: Category123Handler,
    private readonly categorySimpleHandler: CategorySimpleHandler,
  ) {
    this.initializeHandlers();
    this.buildCategoryMap();
  }

  /**
   * 初始化所有处理器
   * 使用依赖注入的handler实例，而不是手动创建
   */
  private initializeHandlers(): void {
    this.handlers = [
      // 复杂业务逻辑处理器
      this.category44Handler,
      this.category46Handler,
      this.category68And204Handler,
      this.category72Handler,
      this.category17And203Handler,
      this.category37Handler,
      this.categoryJudicialHandler,
      this.category107Handler,
      this.categoryEquityHandler,
      this.category114Handler,
      this.category123Handler,

      // 简单处理器 (放在最后，作为兜底)
      this.categorySimpleHandler,
    ];
  }

  /**
   * 构建类别到处理器的映射关系
   */
  private buildCategoryMap(): void {
    this.categoryHandlerMap.clear();

    for (const handler of this.handlers) {
      const supportedCategories = handler.getSupportedCategories();
      for (const category of supportedCategories) {
        if (!this.categoryHandlerMap.has(category)) {
          this.categoryHandlerMap.set(category, handler);
        }
      }
    }
  }

  /**
   * 根据风险变更类别获取对应的处理器
   * @param category 风险变更类别
   * @returns 对应的处理器，如果没有找到则返回 null
   */
  public getHandler(category: RiskChangeCategoryEnum): AbstractCategoryHandler | null {
    return this.categoryHandlerMap.get(category) || null;
  }

  /**
   * 处理风险变更数据项
   * @param item 风险变更数据项
   * @param dimension 维度策略配置
   * @param params 查询参数
   * @returns 是否命中
   */
  public async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const handler = this.getHandler(item.Category);

    if (!handler) {
      // 如果没有找到对应的处理器，记录警告并返回 false
      console.warn(`No handler found for category: ${item.Category}`);
      return false;
    }

    try {
      return await handler.process(item, dimension, params);
    } catch (error) {
      console.error(`Error processing category ${item.Category}:`, error);
      return false;
    }
  }

  /**
   * 获取所有支持的风险变更类别
   * @returns 支持的类别列表
   */
  public getSupportedCategories(): RiskChangeCategoryEnum[] {
    return Array.from(this.categoryHandlerMap.keys());
  }

  /**
   * 获取处理器统计信息
   * @returns 处理器统计信息
   */
  public getHandlerStats(): {
    totalHandlers: number;
    totalCategories: number;
    handlerDetails: Array<{ handlerName: string; categoriesCount: number; categories: RiskChangeCategoryEnum[] }>;
  } {
    const handlerDetails = this.handlers.map((handler) => ({
      handlerName: handler.constructor.name,
      categoriesCount: handler.getSupportedCategories().length,
      categories: handler.getSupportedCategories(),
    }));

    return {
      totalHandlers: this.handlers.length,
      totalCategories: this.categoryHandlerMap.size,
      handlerDetails,
    };
  }
}
