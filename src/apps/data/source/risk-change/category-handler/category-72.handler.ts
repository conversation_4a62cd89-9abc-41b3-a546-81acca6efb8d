import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { Logger } from 'log4js';
import { AbstractCategoryHandler } from './abstract-category.handler';

@Injectable()
export class Category72Handler extends AbstractCategoryHandler {
  private readonly logger: Logger = QccLogger.getLogger(Category72Handler.name);
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }
  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [RiskChangeCategoryEnum.category72];
  }
  async process(itemRaw: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(itemRaw);
    let isHit = true;
    const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
    if (layTypesField && isHit) {
      isHit = this.riskChangeHelper.hitLayTypesField72(layTypesField, itemRaw);
    }
    const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
    if (isBPField && isHit) {
      const IsBP = newItem?.ChangeExtend?.PartInfo?.IsBP ? Number(newItem.ChangeExtend.PartInfo.IsBP) : null;
      newItem.ChangeExtend.IsBP = IsBP;
      isHit = this.riskChangeHelper.hitIsBPField(isBPField, newItem);
    }
    // PartInfo  D 股份下降， H 股份上升，F 股份新增
    const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
    const actorKeyNoHits = [];
    if (holderRoleField && isHit) {
      const { hit, hitKeyNos } = await this.riskChangeHelper.category72holderRoleField(holderRoleField, newItem, params.keyNo);
      isHit = hit;
      if (hitKeyNos?.length) {
        actorKeyNoHits.push(...hitKeyNos);
      }
    }
    // 变更趋势下降
    const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
    if (shareChangeStatusField && isHit) {
      isHit = this.riskChangeHelper.category72ShareChangeStatusField(shareChangeStatusField, newItem, actorKeyNoHits);
    }
    // 是否是PEVC融资
    const isPEVCField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isPEVC);
    if (isPEVCField && isHit) {
      isHit = await this.riskChangeHelper.category72isPEVCField(isPEVCField, newItem, params.keyNo);
    }
    // 周期内持股比例变更
    const periodShareRatioChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodShareRatioChange);
    if (periodShareRatioChangeField && isHit) {
      const periodShareRatioChangeFieldFieldValue = periodShareRatioChangeField.fieldValue[0] as any;
      const timePeriod = periodShareRatioChangeFieldFieldValue?.timePeriod || null;
      if (!timePeriod) {
        isHit = false;
      }
      const periodRes = await this.riskChangeHelper.commonCivilRiskChange([params.keyNo], [RiskChangeCategoryEnum.category72], timePeriod, 'month', 10000);
      if (periodRes.Paging.TotalRecords > 10000) {
        // 先记录，超过10000 ，代码需要优化改成游标查询
        this.logger.error(`RiskChange category72 Max WindowSize CompanyId ${params.keyNo}`);
      }
      isHit = await this.riskChangeHelper.category72periodShareRatioChangeField(
        periodShareRatioChangeField,
        newItem,
        params.keyNo,
        periodRes.Result,
        actorKeyNoHits[0],
      );
    }
    return isHit;
  }
}
