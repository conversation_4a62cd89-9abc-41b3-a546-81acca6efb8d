import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { AbstractCategoryHandler } from './abstract-category.handler';

/**
 * 受益所有人变更处理器 (Category 114)
 *
 * 处理受益所有人变更的业务逻辑：
 * - 股份变更状态验证
 * - 股东角色验证
 * - 变更前后内容验证
 */
@Injectable()
export class Category114Handler extends AbstractCategoryHandler {
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [RiskChangeCategoryEnum.category114];
  }

  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;

    // 股份变更状态验证
    const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
    if (shareChangeStatusField && isHit) {
      isHit = await this.riskChangeHelper.category114shareChangeStatusField(shareChangeStatusField, newItem);
    }

    // 股东角色验证
    const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
    if (holderRoleField && isHit) {
      isHit = await this.riskChangeHelper.category114holderRoleField(holderRoleField, newItem, params.keyNo);
    }

    // 变更前内容验证
    const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
    if (beforeContentField && isHit) {
      isHit = await this.riskChangeHelper.category114beforeContentField(beforeContentField, newItem);
    }

    // 变更后内容验证
    const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
    if (afterContentField && isHit) {
      isHit = await this.riskChangeHelper.category114afterContentField(afterContentField, newItem);
    }

    return isHit;
  }
}
