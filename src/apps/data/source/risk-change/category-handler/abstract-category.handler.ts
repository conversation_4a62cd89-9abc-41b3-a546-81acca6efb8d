import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';

export abstract class AbstractCategoryHandler {
  /**
   *
   * @param newItem 对 newItem 中的 JSON 字段进行预处理：
   *    - Extend1: 解析为 JSON 对象
   *    - ChangeExtend: 解析为 JSON 对象
   * @returns
   */
  protected preProcess(newItem: RiskChangeItem): RiskChangeItem {
    if (!newItem) {
      return newItem;
    }
    Object.keys(newItem).forEach((key) => {
      if (['Extend1', 'ChangeExtend'].includes(key)) {
        const value = newItem[key];
        try {
          newItem[key] = value ? JSON.parse(value) : {};
        } catch (error) {
          newItem[key] = value;
        }
      }
    });
    return newItem;
  }

  supportsCategory(category: RiskChangeCategoryEnum): boolean {
    return this.getSupportedCategories()?.includes(category);
  }

  abstract getSupportedCategories(): RiskChangeCategoryEnum[];

  abstract process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params?: HitDetailsBaseQueryParams): Promise<boolean>;
}
