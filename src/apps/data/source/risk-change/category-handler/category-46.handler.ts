import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import * as Bluebird from 'bluebird';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { Logger } from 'log4js';
import { AbstractCategoryHandler } from './abstract-category.handler';
import { PersonData } from 'libs/model/data/source/PersonData';
import { PersonHelper } from 'apps/data/helper/person.helper';
export class Category46Handler extends AbstractCategoryHandler {
  private readonly logger: Logger = QccLogger.getLogger(Category46Handler.name);
  constructor(private readonly riskChangeHelper: RiskChangeHelper, private readonly personHelper: PersonHelper) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [RiskChangeCategoryEnum.category46];
  }
  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    let isHit = true;
    const newItem = this.preProcess(item);
    const compChangeRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.compChangeRole);
    if (compChangeRoleField && isHit) {
      isHit = this.riskChangeHelper.hitCompChangeRoleField(compChangeRoleField, newItem);
    }
    const baselineDateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.baselineDate);
    const changeThresholdField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.changeThreshold);
    if (baselineDateField && changeThresholdField && compChangeRoleField && isHit) {
      const baselineDateFieldTargetValues = baselineDateField?.fieldValue as number[];
      if (baselineDateFieldTargetValues?.length) {
        const periodRes = await this.riskChangeHelper.commonCivilRiskChange(
          [params.keyNo],
          [RiskChangeCategoryEnum.category46],
          baselineDateFieldTargetValues[0],
          'year',
          10000,
        );
        if (periodRes.Paging.TotalRecords > 10000) {
          // TODO 先记录，超过10000 ，代码需要优化改成游标查询
          this.logger.error(`RiskChange category46 Max WindowSize CompanyId ${params.keyNo}`);
        }
        if (compChangeRoleField && changeThresholdField && isHit && periodRes?.Result?.length) {
          // 获取当前所有人的信息
          const personDatas: PersonData[] = [];
          const [list1, list2] = await Bluebird.all([
            this.personHelper.getEmployeeData(params.keyNo, 'IpoEmployees'),
            this.personHelper.getEmployeeData(params.keyNo, 'Employees'),
          ]);
          personDatas.push(...list1, ...list2);
          isHit = this.riskChangeHelper.changeThresholdField(compChangeRoleField, changeThresholdField, newItem, periodRes?.Result as any[], personDatas);
        }
      }
    }
    return isHit;
  }
}
