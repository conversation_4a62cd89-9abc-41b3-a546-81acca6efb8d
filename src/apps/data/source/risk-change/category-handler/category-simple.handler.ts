import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { AbstractCategoryHandler } from './abstract-category.handler';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { BaseHelper } from '../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';

export class CategorySimpleHandler extends AbstractCategoryHandler {
  constructor(private readonly baseHelper: BaseHelper, private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }
  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [
      // 新闻相关
      RiskChangeCategoryEnum.category62,
      RiskChangeCategoryEnum.category66,
      RiskChangeCategoryEnum.category67,
      // 基础变更
      RiskChangeCategoryEnum.category39,
      RiskChangeCategoryEnum.category139,
      // 抵押担保
      RiskChangeCategoryEnum.category15,
      RiskChangeCategoryEnum.category30,
      RiskChangeCategoryEnum.category53,
      RiskChangeCategoryEnum.category101,
      // 税务相关
      RiskChangeCategoryEnum.category131,
      RiskChangeCategoryEnum.category31,
      RiskChangeCategoryEnum.category117,
      // 经营状态相关
      RiskChangeCategoryEnum.category38,
      RiskChangeCategoryEnum.category11,
      RiskChangeCategoryEnum.category55,
      RiskChangeCategoryEnum.category58,
      // 司法拍卖
      RiskChangeCategoryEnum.category57,
      // 处罚相关
      RiskChangeCategoryEnum.category22,
      RiskChangeCategoryEnum.category121,
      RiskChangeCategoryEnum.category14,
      // 知识产权
      RiskChangeCategoryEnum.category86,
      // 安全相关
      RiskChangeCategoryEnum.category79,
      // 注销相关
      RiskChangeCategoryEnum.category23,
      // 融资动态
      RiskChangeCategoryEnum.category28,
      // 企业公告
      RiskChangeCategoryEnum.category65,
      RiskChangeCategoryEnum.category113,
      // 评估拍卖
      RiskChangeCategoryEnum.category59,
      RiskChangeCategoryEnum.category75,
      // 空逻辑cases
      RiskChangeCategoryEnum.category98,
      RiskChangeCategoryEnum.category78,
      RiskChangeCategoryEnum.category61,
      RiskChangeCategoryEnum.category108,
      RiskChangeCategoryEnum.category76,
    ];
  }
  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;

    switch (item.Category) {
      // 负面/正面新闻
      case RiskChangeCategoryEnum.category62:
      case RiskChangeCategoryEnum.category66:
      case RiskChangeCategoryEnum.category67: {
        const holderRoles = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
        const topics = this.riskChangeHelper.getDimesionTopics(dimension);
        if (holderRoles && topics) {
          isHit = await this.baseHelper.hitNegativePositiveNewsField(holderRoles, topics, newItem, params.keyNo);
        }
        break;
      }
      // 法定代表人变更
      case RiskChangeCategoryEnum.category39: {
        const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
        if (layTypesField) {
          isHit = this.riskChangeHelper.hitLayTypesField(layTypesField, newItem);
        }
        break;
      }
      // 经营地址变更
      case RiskChangeCategoryEnum.category139: {
        isHit = this.baseHelper.filterLastYearData(newItem);
        break;
      }
      // 动产抵押
      case RiskChangeCategoryEnum.category15: {
        const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteedPrincipal);
        if (riskCategories && isHit) {
          isHit = this.riskChangeHelper.category15Field(riskCategories, newItem);
        }
        break;
      }
      // 土地抵押
      case RiskChangeCategoryEnum.category30: {
        const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.landMortgageAmount);
        if (riskCategories && isHit) {
          isHit = this.riskChangeHelper.category30Field(params.keyNo, riskCategories, newItem);
        }
        break;
      }
      // 提供担保
      case RiskChangeCategoryEnum.category53:
      case RiskChangeCategoryEnum.category101: {
        const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteeAmount);
        if (riskCategories && isHit) {
          isHit = this.riskChangeHelper.category101Field(riskCategories, newItem);
        }
        break;
      }
      // 税务催缴
      case RiskChangeCategoryEnum.category131: {
        const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.AmountOwed);
        if (riskCategories && isHit) {
          isHit = this.riskChangeHelper.category131Field(riskCategories, newItem);
        }
        break;
      }
      // 欠税公告
      case RiskChangeCategoryEnum.category31: {
        const taxOwedAmountTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.taxOwedAmount);
        if (taxOwedAmountTypeField && isHit) {
          isHit = this.riskChangeHelper.amountField(taxOwedAmountTypeField, newItem?.ChangeExtend?.B, 10000);
        }
        const penaltyUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
        if (penaltyUnitField && isHit) {
          isHit = this.riskChangeHelper.penaltyUnitField31(penaltyUnitField, newItem);
        }
        const punishAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishAmount);
        if (punishAmountField && isHit) {
          isHit = this.riskChangeHelper.checkAmountField(punishAmountField, newItem?.ChangeExtend?.F, 1);
        }
        break;
      }
      // 被列入税务非正常户
      case RiskChangeCategoryEnum.category117: {
        const penaltyUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
        if (penaltyUnitField && isHit) {
          isHit = this.riskChangeHelper.penaltyUnitField117(penaltyUnitField, newItem);
        }
        break;
      }
      // 经营状态变更
      case RiskChangeCategoryEnum.category38: {
        const businessStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessStatus);
        if (businessStatusField && isHit) {
          isHit = this.riskChangeHelper.category38(businessStatusField, newItem);
        }
        break;
      }
      // 经营异常
      case RiskChangeCategoryEnum.category11: {
        const businessAbnormalTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessAbnormalType);
        if (businessAbnormalTypeField && isHit) {
          isHit = this.riskChangeHelper.businessAbnormalTypeField(businessAbnormalTypeField, newItem);
        }
        break;
      }
      // 被限制高消费
      case RiskChangeCategoryEnum.category55: {
        const restrictTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.restricterType);
        if (restrictTypeField && isHit) {
          isHit = this.riskChangeHelper.restricterTypeField(restrictTypeField, newItem);
        }
        break;
      }
      // 破产重整
      case RiskChangeCategoryEnum.category58: {
        isHit = this.riskChangeHelper.category58Field(newItem);
        break;
      }
      // 司法拍卖
      case RiskChangeCategoryEnum.category57: {
        const auctionTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.auctionType);
        if (auctionTypeField && isHit) {
          isHit = this.riskChangeHelper.auctionTypeField(auctionTypeField, newItem);
        }
        const limitPriceField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.listingPrice);
        if (limitPriceField && isHit) {
          isHit = this.riskChangeHelper.limitPriceTypeField(limitPriceField, newItem);
        }
        break;
      }
      // 环保处罚
      case RiskChangeCategoryEnum.category22: {
        const punishEnvTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishType);
        if (punishEnvTypeField && isHit) {
          isHit = this.riskChangeHelper.punishEnvTypeField(punishEnvTypeField, newItem);
        }
        const punishAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltiesAmount);
        if (punishAmountField && isHit) {
          isHit = this.riskChangeHelper.amountField(punishAmountField, newItem?.ChangeExtend?.E, 1);
        }
        const punishRedCardField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishRedCard);
        if (punishRedCardField && isHit) {
          isHit = this.riskChangeHelper.penaltyRedCardFieldCategory22(punishRedCardField, newItem);
        }
        break;
      }
      // 金融监管
      case RiskChangeCategoryEnum.category121: {
        const financialPenaltyCauseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.financialPenaltyCause);
        if (financialPenaltyCauseTypeField && isHit) {
          isHit = this.riskChangeHelper.financialPenaltyCauseTypeField(financialPenaltyCauseTypeField, newItem);
        }
        break;
      }
      // 抽查检查
      case RiskChangeCategoryEnum.category14: {
        const inspectionResultTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.inspectionResultType);
        if (inspectionResultTypeField && isHit) {
          isHit = this.riskChangeHelper.inspectionResultTypeField(inspectionResultTypeField, newItem);
        }
        break;
      }
      // 知识产权
      case RiskChangeCategoryEnum.category86: {
        const intellectualRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.intellectualRole);
        if (intellectualRoleField && isHit) {
          isHit = this.riskChangeHelper.category86IntellectualRole(intellectualRoleField, newItem);
        }
        const intellectualTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.intellectualType);
        if (intellectualTypeField && isHit) {
          isHit = this.riskChangeHelper.category86IntellectualType(intellectualTypeField, newItem);
        }
        break;
      }
      // 食品安全
      case RiskChangeCategoryEnum.category79: {
        const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.productSource);
        if (typeField && isHit) {
          isHit = this.riskChangeHelper.category79Field(typeField, newItem);
        }
        break;
      }
      // 简易注销
      case RiskChangeCategoryEnum.category23: {
        const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.simpleCancelType);
        if (typeField && isHit) {
          isHit = this.riskChangeHelper.category23Field(typeField, newItem);
        }
        break;
      }
      // 融资动态
      case RiskChangeCategoryEnum.category28: {
        const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.financingDynamicType);
        if (typeField && isHit) {
          isHit = this.riskChangeHelper.category28Field(typeField, newItem);
        }
        break;
      }
      // 企业公告
      case RiskChangeCategoryEnum.category65:
      case RiskChangeCategoryEnum.category113: {
        const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.announcementReportType);
        if (isHit && typeField) {
          isHit = this.riskChangeHelper.categoryAnnouncementReportField(typeField, newItem);
        }
        break;
      }
      // 询价评估
      case RiskChangeCategoryEnum.category59: {
        if (isHit && dimension.strategyFields) {
          const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.evaluationPrice);
          if (typeField && isHit) {
            isHit = this.riskChangeHelper.category59Field(typeField, newItem);
          }
        }
        break;
      }
      // 资产拍卖
      case RiskChangeCategoryEnum.category75: {
        if (isHit && dimension.strategyFields) {
          const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.quoteResultPrice);
          if (typeField && isHit) {
            isHit = this.riskChangeHelper.category75Field(typeField, newItem);
          }
        }
        break;
      }
      // 空逻辑cases (无处理逻辑)
      case RiskChangeCategoryEnum.category98: // 未准入境
      case RiskChangeCategoryEnum.category78: // 产品召回
      case RiskChangeCategoryEnum.category61: // 注销备案
      case RiskChangeCategoryEnum.category108: // 票据违约
      case RiskChangeCategoryEnum.category76: // 询价评估-机构
        break;
    }

    return isHit;
  }
}
