import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { AbstractCategoryHandler } from './abstract-category.handler';

/**
 * 持股比例变更处理器 (Category 68, 204)
 *
 * 处理持股比例变更的业务逻辑：
 * - 股份变更状态验证 (上升/下降)
 * - 变更前后持股比例验证
 * - 大股东变更状态验证
 * - 股份变更率验证
 * - 股东角色验证
 */
@Injectable()
export class Category68And204Handler extends AbstractCategoryHandler {
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [RiskChangeCategoryEnum.category204, RiskChangeCategoryEnum.category68];
  }

  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;

    // 股份变更状态验证
    const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
    if (shareChangeStatusField && isHit) {
      isHit = this.riskChangeHelper.hitShareChangeStatusField(shareChangeStatusField, newItem);
    }

    // 变更前持股比例验证
    const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
    if (beforeContentField && isHit) {
      isHit = this.riskChangeHelper.hitBeforeContentField(beforeContentField, newItem);
    }

    // 变更后持股比例验证
    const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
    if (afterContentField && isHit) {
      isHit = this.riskChangeHelper.hitAfterContentField(afterContentField, newItem);
    }

    // 大股东变更状态验证
    const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
    if (isBPField && isHit) {
      isHit = this.riskChangeHelper.hitIsBPField(isBPField, newItem);
    }

    // 股份变更率验证
    const shareChangeRateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeRate);
    if (shareChangeRateField && isHit) {
      isHit = this.riskChangeHelper.hitShareChangeRateField(shareChangeRateField, newItem);
    }

    // 股东角色验证
    const keyNoHits: string[] = [];
    const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
    if (holderRoleField && isHit) {
      const { hit, hitKeyNos } = await this.riskChangeHelper.hitHolderRoleField(holderRoleField, [params.keyNo], newItem.ChangeExtend.K);
      isHit = hit;
      if (hitKeyNos?.length) {
        keyNoHits.push(...hitKeyNos);
      }
    }

    return isHit;
  }
}
