import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { AbstractCategoryHandler } from './abstract-category.handler';

/**
 * 行政处罚处理器 (Category 107)
 *
 * 处理行政处罚的业务逻辑：
 * - 处罚单位验证
 * - 处罚类型验证
 * - 处罚金额验证
 * - 处罚红卡验证
 * - 处罚颁发单位验证
 * - 上市状态验证
 */
@Injectable()
export class Category107Handler extends AbstractCategoryHandler {
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [RiskChangeCategoryEnum.category107];
  }

  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;

    // 处罚单位验证
    const penaltyUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
    if (penaltyUnitField && isHit) {
      isHit = this.riskChangeHelper.penaltyUnitField(penaltyUnitField, newItem);
    }

    // 处罚类型验证
    const punishTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishType);
    if (punishTypeField && isHit) {
      isHit = this.riskChangeHelper.punishTypeField(punishTypeField, newItem);
    }

    // 处罚金额验证
    const punishAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishAmount);
    if (punishAmountField && isHit) {
      isHit = this.riskChangeHelper.checkAmountField(punishAmountField, newItem?.ChangeExtend?.F, 1);
    }

    // 处罚红卡验证
    const punishRedCardField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishRedCard);
    if (punishRedCardField && isHit) {
      isHit = this.riskChangeHelper.penaltyRedCardFieldCategory107(punishRedCardField, newItem);
    }

    // 处罚颁发单位验证
    const penaltyIssuingUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyIssuingUnit);
    if (penaltyIssuingUnitField && isHit) {
      isHit = this.riskChangeHelper.penaltyIssuingUnitField(penaltyIssuingUnitField, newItem);
    }

    // 上市状态验证
    const isListedField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isListed);
    if (isListedField && isHit) {
      isHit = await this.riskChangeHelper.checkListedField(isListedField, newItem, params.keyNo);
    }

    return isHit;
  }
}
