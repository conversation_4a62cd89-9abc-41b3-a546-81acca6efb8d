import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { CompanyChangeHelper } from 'apps/data/source/risk-change/helper/company-change.helper';
import { CompanyShareHelper } from 'apps/data/source/risk-change/helper/company-share.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { Logger } from 'log4js';
import { AbstractCategoryHandler } from './abstract-category.handler';

/**
 * 股东股份变更处理器 (Category 44)
 *
 * 处理股东股份变更的复杂业务逻辑：
 * - 股比变更趋势判断 (上升/下降)
 * - 股东角色验证
 * - 差值比例和绝对值比例计算
 * - 变更前后持股比例验证
 *
 * ChangeExtend 字段结构：
 * - A: 股比下降count
 * - B: 退出count
 * - C: 新增count
 * - D: 股比下降列表
 * - E: 退出列表
 * - F: 新增列表
 * - G: 上升count
 * - H: 上升列表
 * - IsBP: 是否有大股东变更 (1有 0无)
 * - BP: 大股东变更列表
 */
@Injectable()
export class Category44Handler extends AbstractCategoryHandler {
  private readonly logger: Logger = QccLogger.getLogger(Category44Handler.name);

  constructor(
    private readonly riskChangeHelper: RiskChangeHelper,
    private readonly companyChangeHelper: CompanyChangeHelper,
    private readonly companyShareHelper: CompanyShareHelper,
  ) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [RiskChangeCategoryEnum.category44];
  }

  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;
    let holders = [];

    // 目前只处理股比上升或下降，不包含股东退出或者新增
    const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
    if (shareChangeStatusField && isHit) {
      isHit = false;
      if (shareChangeStatusField.fieldValue.includes(0) && newItem.ChangeExtend.A > 0) {
        // 股比下降 newItem.ChangeExtend.A > 0
        isHit = true;
        holders = newItem.ChangeExtend.D;
      }
      if (shareChangeStatusField.fieldValue.includes(1) && newItem.ChangeExtend.G > 0) {
        // 股比上升
        isHit = true;
        holders = newItem.ChangeExtend.H;
      }
    }

    // 股权角色验证
    const keyNoHits: string[] = [];
    const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
    if (holderRoleField && isHit && holders.length) {
      const holderKeys = holders.map((d) => d.K);
      const { hit, hitKeyNos } = await this.riskChangeHelper.hitHolderRoleField(holderRoleField, holderKeys, params.keyNo);
      isHit = hit;
      if (hitKeyNos?.length) {
        keyNoHits.push(...hitKeyNos);
      }
    }

    // 差值比例验证
    const differenceRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.differenceRatio);
    if (differenceRatioField && isHit) {
      isHit = this.companyShareHelper.hitDifferenceRatioField(differenceRatioField, holders, keyNoHits);
    }

    // 绝对值比例验证
    const absRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.absRatio);
    if (absRatioField && isHit) {
      isHit = this.companyShareHelper.hitAbsRatioField(absRatioField, holders, keyNoHits);
    }

    // 变更前持股比例验证
    const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
    if (beforeContentField && isHit && keyNoHits?.length) {
      const holder = holders.find((h) => h.K === keyNoHits[0]);
      if (holder?.B) {
        newItem.BeforeContent = holder.B;
      }
      isHit = this.companyChangeHelper.hitBeforeContentField(beforeContentField, newItem);
    }

    // 变更后持股比例验证
    const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
    if (afterContentField && isHit) {
      const holder = holders.find((h) => h.K === keyNoHits[0]);
      if (holder?.C) {
        newItem.AfterContent = holder.C;
      }
      isHit = this.companyChangeHelper.hitAfterContentField(afterContentField, newItem);
    }

    return isHit;
  }
}
