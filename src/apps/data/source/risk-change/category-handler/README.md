# Category Handler 拆分说明

本目录包含了从 `DimensionHitDetailProcessor` 的巨大 switch case 中拆分出来的各个风险变更类别处理器。

## 拆分规则

按照以下规则进行拆分：

1. **复杂业务逻辑**：包含多个字段验证、数据查询、复杂计算的 case，单独创建一个处理器文件
2. **简单处理**：只是简单的方法转发或单一字段验证的 case，放入 `category-simple.handler.ts`

## 文件结构

### 抽象基类

- `abstract-category.handler.ts` - 定义所有处理器的基础结构和通用方法

### 复杂业务逻辑处理器

#### 股权变更相关

- `category-44.handler.ts` - 股东股份变更 (Category 44)

  - 复杂的股份上升/下降逻辑
  - 股东角色验证
  - 差值比例和绝对值比例计算
  - 变更前后持股比例验证

- `category-68-204.handler.ts` - 持股比例变更 (Categories 68, 204)

  - 股份变更状态验证
  - 变更前后持股比例验证
  - 大股东变更状态验证
  - 股份变更率验证

- `category-equity.handler.ts` - 股权相关 (Categories 26, 12, 50)
  - 26: 股权冻结 - 冻结范围、人员、金额验证
  - 12: 股权出质 - 出质比例、状态、数额验证
  - 50: 股权质押 - 质押比例、状态、数量验证

#### 投资变更相关

- `category-17-203.handler.ts` - 对外投资变更 (Categories 17, 203)

  - 变更状态验证
  - 变更前后内容验证
  - 时间周期内投资变更次数验证
  - 变更阈值验证

- `category-37.handler.ts` - 注册资本变更 (Category 37)
  - 币种变更验证
  - 注册资本趋势验证
  - 注册资本变更比例验证
  - 周期内注册资本变更验证

#### 人员变更相关

- `category-46.handler.ts` - 主要人员变更 (Category 46)

  - 人员角色变更验证
  - 基准日期和变更阈值验证
  - 获取员工数据进行比对

- `category-72.handler.ts` - 股东变更 (Category 72)

  - 多种股权变更字段验证
  - PEVC 融资验证
  - 周期内持股比例变更验证

- `category-114.handler.ts` - 受益所有人变更 (Category 114)
  - 股份变更状态验证
  - 股东角色验证
  - 变更前后内容验证

#### 司法案件相关

- `category-judicial.handler.ts` - 司法案件 (Categories 4, 49, 18, 7, 27, 90)
  - 4: 裁判文书, 49: 立案信息, 18: 开庭公告
  - 7: 法院公告, 27: 送达公告, 90: 诉前调解
  - 司法角色、案件类型、金额等多字段验证

#### 处罚相关

- `category-107.handler.ts` - 行政处罚 (Category 107)

  - 处罚单位验证
  - 处罚类型和金额验证
  - 处罚红卡验证
  - 上市状态验证

- `category-123.handler.ts` - 减资公告 (Category 123)
  - 币种变更验证
  - 资本减少比例验证
  - 周期内注册资本验证

### 简单处理器

- `category-simple.handler.ts` - 包含所有简单处理逻辑的 case
  - 新闻相关: Categories 62, 66, 67
  - 基础变更: Categories 39, 139
  - 抵押担保: Categories 15, 30, 53, 101
  - 税务相关: Categories 131, 31, 117
  - 经营状态: Categories 38, 11, 55, 58
  - 司法拍卖: Category 57
  - 处罚相关: Categories 22, 121, 14
  - 知识产权: Category 86
  - 安全相关: Category 79
  - 注销相关: Category 23
  - 融资动态: Category 28
  - 企业公告: Categories 65, 113
  - 评估拍卖: Categories 59, 75
  - 空逻辑: Categories 98, 78, 61, 108, 76

## 使用方式

### 1. 处理器基础结构

每个处理器都继承自 `AbstractCategoryHandler`，实现以下方法：

- `getSupportedCategories()` - 返回支持的风险变更类别
- `process()` - 处理具体的业务逻辑
- `preProcess()` - 预处理 JSON 字段（继承自基类）

### 2. 通过 Factory 使用处理器

推荐使用 `CategoryHandlerFactory` 来统一管理和调用处理器：

```typescript
import { CategoryHandlerFactory } from './category-handler.factory';

@Injectable()
export class YourService {
  constructor(private readonly categoryHandlerFactory: CategoryHandlerFactory) {}

  async processRiskChangeItem(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    // 通过factory处理风险变更数据
    return await this.categoryHandlerFactory.process(item, dimension, params);
  }

  // 获取特定类别的处理器
  getSpecificHandler(category: RiskChangeCategoryEnum): AbstractCategoryHandler | null {
    return this.categoryHandlerFactory.getHandler(category);
  }

  // 获取所有支持的类别
  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return this.categoryHandlerFactory.getSupportedCategories();
  }

  // 获取处理器统计信息
  getHandlerStats() {
    return this.categoryHandlerFactory.getHandlerStats();
  }
}
```

### 3. 依赖注入管理

所有处理器都是 NestJS 的 `@Injectable()` 服务，通过依赖注入管理：

- **Factory 自动注入**：`CategoryHandlerFactory` 会自动注入所有需要的处理器实例
- **无需手动实例化**：不需要使用 `new` 关键字创建处理器
- **依赖管理**：处理器的依赖（如各种 Helper）也通过依赖注入自动管理

### 4. 直接使用特定处理器

如果需要直接使用特定的处理器，也可以直接注入：

```typescript
@Injectable()
export class YourService {
  constructor(private readonly category37Handler: Category37Handler, private readonly category44Handler: Category44Handler) {}

  async processCategory37(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    return await this.category37Handler.process(item, dimension, params);
  }
}
```

## 设计原则

1. **单一职责**：每个处理器只处理相关的风险变更类别
2. **逻辑清晰**：复杂逻辑独立成文件，简单逻辑归类到统一文件
3. **易于维护**：通过拆分降低单个文件的复杂度
4. **保持兼容**：不修改原有业务逻辑，只是重新组织代码结构
5. **依赖注入**：所有组件通过 NestJS 依赖注入管理，避免手动实例化
6. **直接调用**：优先使用直接方法调用，避免不必要的转发方法

## 优化说明

### 转发方法优化

在重构过程中，我们将原本通过 `RiskChangeHelper` 转发的方法改为直接调用原始方法：

- **优化前**：`this.riskChangeHelper.hitCurrencyChangeField()` → 转发到 `this.companyChangeHelper.hitCurrencyChangeField()`
- **优化后**：直接调用 `this.companyChangeHelper.hitCurrencyChangeField()`

这样的优化使代码更简洁，减少了不必要的中间层。

这样的拆分和优化使得代码更容易理解、测试和维护。
