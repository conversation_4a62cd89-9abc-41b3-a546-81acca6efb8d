import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { processAmountString } from 'libs/utils/utils';
import { AbstractCategoryHandler } from './abstract-category.handler';

/**
 * 司法案件处理器 (Categories 4, 49, 18, 7, 27, 90)
 *
 * 处理司法案件相关的业务逻辑：
 * - 4: 裁判文书
 * - 49: 立案信息
 * - 18: 开庭公告
 * - 7: 法院公告
 * - 27: 送达公告
 * - 90: 诉前调解
 *
 * 共同验证字段：
 * - 司法角色验证
 * - 案件原因类型验证
 * - 案件类型验证
 * - 合同纠纷验证
 * - 金融原因验证
 * - 银行或金融租赁验证
 * - 诉讼金额验证 (仅裁判文书)
 */
@Injectable()
export class CategoryJudicialHandler extends AbstractCategoryHandler {
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [
      RiskChangeCategoryEnum.category4, // 裁判文书
      RiskChangeCategoryEnum.category49, // 立案信息
      RiskChangeCategoryEnum.category18, // 开庭公告
      RiskChangeCategoryEnum.category7, // 法院公告
      RiskChangeCategoryEnum.category27, // 送达公告
      RiskChangeCategoryEnum.category90, // 诉前调解
    ];
  }

  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;

    // 司法角色验证
    const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
    if (judicialRoleTypeField && isHit) {
      switch (item.Category) {
        case RiskChangeCategoryEnum.category4:
          isHit = this.riskChangeHelper.category4(judicialRoleTypeField, newItem);
          break;
        case RiskChangeCategoryEnum.category49:
          isHit = this.riskChangeHelper.category49(judicialRoleTypeField, newItem);
          break;
        case RiskChangeCategoryEnum.category18:
          isHit = this.riskChangeHelper.category18(judicialRoleTypeField, newItem);
          break;
        case RiskChangeCategoryEnum.category7:
          isHit = this.riskChangeHelper.category7(judicialRoleTypeField, newItem);
          break;
        case RiskChangeCategoryEnum.category27:
          isHit = this.riskChangeHelper.category27(judicialRoleTypeField, newItem);
          break;
        case RiskChangeCategoryEnum.category90:
          isHit = this.riskChangeHelper.category90(judicialRoleTypeField, newItem);
          break;
      }
    }

    // 案件原因类型验证
    const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
    if (caseReasonTypeField && isHit) {
      isHit = this.riskChangeHelper.caseReasonTypeField(caseReasonTypeField, newItem);
    }

    // 案件类型验证
    const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
    if (caseTypeField && isHit) {
      isHit = this.riskChangeHelper.checkCaseTypeField(caseTypeField, newItem);
    }

    // 诉讼金额验证 (仅裁判文书)
    if (item.Category === RiskChangeCategoryEnum.category4) {
      const lawsuitAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.lawsuitAmount);
      if (lawsuitAmountField && isHit) {
        isHit = this.riskChangeHelper.checkAmountField(lawsuitAmountField, processAmountString(newItem?.ChangeExtend?.I), 1);
      }
    }

    // 合同纠纷验证
    const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
    if (isContractDisputeField && isHit) {
      isHit = this.riskChangeHelper.checkContractDisputeField(isContractDisputeField, newItem);
    }

    // 金融原因验证
    const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
    if (isFinancialReasonField && isHit) {
      isHit = this.riskChangeHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
    }

    // 银行或金融租赁验证
    const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
    if (isBankOrFlField && isHit) {
      switch (item.Category) {
        case RiskChangeCategoryEnum.category4:
          isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField4(isBankOrFlField, newItem);
          break;
        case RiskChangeCategoryEnum.category49:
          isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField49(isBankOrFlField, newItem);
          break;
        case RiskChangeCategoryEnum.category18:
          isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField18(isBankOrFlField, newItem);
          break;
        case RiskChangeCategoryEnum.category7:
          isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField7(isBankOrFlField, newItem);
          break;
        case RiskChangeCategoryEnum.category27:
          isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField27(isBankOrFlField, newItem);
          break;
        case RiskChangeCategoryEnum.category90:
          isHit = this.riskChangeHelper.checkBankOrFinancialLeasingField90(isBankOrFlField, newItem);
          break;
      }
    }

    return isHit;
  }
}
