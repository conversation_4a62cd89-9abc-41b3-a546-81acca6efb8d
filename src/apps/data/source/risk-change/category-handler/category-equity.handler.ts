import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { AbstractCategoryHandler } from './abstract-category.handler';

/**
 * 股权相关处理器 (Categories 26, 12, 50)
 *
 * 处理股权相关的业务逻辑：
 * - 26: 股权冻结
 * - 12: 股权出质
 * - 50: 股权质押
 *
 * 每个类别有各自特定的验证字段
 */
@Injectable()
export class CategoryEquityHandler extends AbstractCategoryHandler {
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [
      RiskChangeCategoryEnum.category26, // 股权冻结
      RiskChangeCategoryEnum.category12, // 股权出质
      RiskChangeCategoryEnum.category50, // 股权质押
    ];
  }

  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;

    switch (item.Category) {
      case RiskChangeCategoryEnum.category26: {
        // 股权冻结处理
        isHit = await this.processCategory26(newItem, dimension, params);
        break;
      }
      case RiskChangeCategoryEnum.category12: {
        // 股权出质处理
        isHit = await this.processCategory12(newItem, dimension, params);
        break;
      }
      case RiskChangeCategoryEnum.category50: {
        // 股权质押处理
        isHit = await this.processCategory50(newItem, dimension, params);
        break;
      }
    }

    return isHit;
  }

  /**
   * 处理股权冻结 (Category 26)
   */
  private async processCategory26(newItem: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    let isHit = true;

    // 股权冻结范围验证
    const equityFreezeScopeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityFreezeScope);
    if (equityFreezeScopeField && isHit) {
      isHit = this.riskChangeHelper.equityFreezeScopeFieldCategory26(equityFreezeScopeField, newItem);
    }

    // 主要人员验证
    const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
    if (holderRoleField && isHit) {
      isHit = await this.riskChangeHelper.holderRoleFieldCategory26(holderRoleField, newItem, params.keyNo);
    }

    // 股权冻结金额验证
    const equityFrozenAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityFrozenAmount);
    if (equityFrozenAmountField && isHit) {
      isHit = this.riskChangeHelper.equityFrozenAmountFieldCategory26(equityFrozenAmountField, newItem);
    }

    return isHit;
  }

  /**
   * 处理股权出质 (Category 12)
   */
  private async processCategory12(newItem: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    let isHit = true;

    // 股权出质比例或持股验证
    const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgedRatioOrHolding);
    if (typeField && isHit) {
      isHit = this.riskChangeHelper.category12Field(typeField, newItem);
    }

    // 主要人员验证
    const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
    if (holderRoleField && isHit) {
      isHit = await this.riskChangeHelper.holderRoleFieldCategory12(holderRoleField, newItem, params.keyNo);
    }

    // 股权出质状态验证
    const equityPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeStatus);
    if (equityPledgeStatusField && isHit) {
      isHit = this.riskChangeHelper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, newItem);
    }

    // 股权出质比例验证
    const equityPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeRatio);
    if (equityPledgeRatioField && isHit) {
      isHit = this.riskChangeHelper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, newItem);
    }

    // 出质股权数额验证
    const equityPledgeAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeAmount);
    if (equityPledgeAmountField && isHit) {
      isHit = this.riskChangeHelper.equityPledgeAmountFieldCategory12(equityPledgeAmountField, newItem);
    }

    // 质押股份数量验证
    const equityPledgeQuantityField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeQuantity);
    if (equityPledgeQuantityField && isHit) {
      isHit = this.riskChangeHelper.equityPledgeQuantityFieldCategory12(equityPledgeQuantityField, newItem);
    }

    return isHit;
  }

  /**
   * 处理股权质押 (Category 50)
   */
  private async processCategory50(newItem: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    let isHit = true;

    // 质押比例或持股验证
    const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.pledgedRatioOrHolding);
    if (typeField && isHit) {
      isHit = this.riskChangeHelper.category50Field(typeField, newItem);
    }

    // 主要人员验证
    const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
    if (holderRoleField && isHit) {
      isHit = await this.riskChangeHelper.holderRoleFieldCategory50(holderRoleField, newItem, params.keyNo);
    }

    // 股权质押状态验证
    const sharePledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.sharePledgeStatus);
    if (sharePledgeStatusField && isHit) {
      isHit = this.riskChangeHelper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, newItem);
    }

    // 质押占总股本比例验证
    const stockPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeRatio);
    if (stockPledgeRatioField && isHit) {
      isHit = this.riskChangeHelper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, newItem);
    }

    // 质押股份数量验证
    const stockPledgeQuantityField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeQuantity);
    if (stockPledgeQuantityField && isHit) {
      isHit = this.riskChangeHelper.stockPledgeQuantityFieldCategory50(stockPledgeQuantityField, newItem);
    }

    return isHit;
  }
}
