import { Test, TestingModule } from '@nestjs/testing';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitDetailProcessor } from '../../dimension-hit-detail.processor';
import { CaseReasonHelper } from '../../helper/case-reason.helper';
import { RelatedDimensionHitDetailProcessor } from '../../related-dimension-hit-detail.processor';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createMockStrategyField = (
  fieldKey: DimensionFieldKeyEnums,
  fieldValue: any[],
  compareType?: DimensionFieldCompareTypeEnums,
): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  field.compareType = compareType;
  return field;
};

describe('RelatedDimensionHitDetailProcessor 关联方类型处理测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getFinalActualController: jest.fn(),
      getPartnerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getInvestCompany: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      commonCivilRiskChange: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      detailAnalyzeForRelated: jest.fn(),
      hitIndustryThresholdField: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      getCaseTitleDescData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn().mockResolvedValue({
      body: {
        hits: {
          total: { value: 0 },
          hits: [],
        },
      },
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('对外投资企业 (InvestCompany) 处理', () => {
    it('应该正确获取对外投资企业列表', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.InvestCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
        createMockStrategyField(DimensionFieldKeyEnums.businessStatus, [1, 2]), // 注销、吊销状态
        createMockStrategyField(DimensionFieldKeyEnums.fundedRatioLevel, [3]), // >20%
      ];

      const mockInvestCompanies = {
        Paging: { TotalRecords: 2 },
        Result: [
          { KeyNo: 'invest1', CompanyName: '投资企业1' },
          { KeyNo: 'invest2', CompanyName: '投资企业2' },
        ],
      };

      mockCompanyDetailService.getInvestCompany.mockResolvedValue(mockInvestCompanies);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockCompanyDetailService.getInvestCompany).toHaveBeenCalledWith(
        'test-company-id',
        3, // fundedRatioLevel
        [1, 2], // status
        200,
      );
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([{ terms: { KeyNo: ['invest1', 'invest2'] } }]),
            }),
          }),
        }),
        'test-company-id',
      );
    });

    it('应该处理没有对外投资企业的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.InvestCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      mockCompanyDetailService.getInvestCompany.mockResolvedValue({
        Paging: { TotalRecords: 0 },
        Result: [],
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });
  });

  describe('实际控制人 (ActualController) 处理', () => {
    it('应该正确获取实际控制人列表', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockActualControllers = [
        { keyNo: 'person1', name: '实控人1' },
        { keyNo: 'person2', name: '实控人2' },
      ];

      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith('test-company-id', false);
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([{ terms: { KeyNo: ['person1', 'person2'] } }]),
            }),
          }),
        }),
        'test-company-id',
      );
    });

    it('应该处理没有实际控制人的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      mockPersonHelper.getFinalActualController.mockResolvedValue([]);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });

    it('应该过滤掉空的keyNo', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockActualControllers = [
        { keyNo: 'person1', name: '实控人1' },
        { keyNo: '', name: '实控人2' }, // 空keyNo
        { keyNo: null, name: '实控人3' }, // null keyNo
        { keyNo: 'person4', name: '实控人4' },
      ];

      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                { terms: { KeyNo: ['person1', 'person4'] } }, // 只包含有效的keyNo
              ]),
            }),
          }),
        }),
        'test-company-id',
      );
    });
  });

  describe('大股东 (MajorShareholder) 处理', () => {
    it('应该正确获取大股东列表', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.MajorShareholder]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockPartners = [
        { keyNo: 'partner1', name: '股东1', tags: ['大股东'] },
        { keyNo: 'partner2', name: '股东2', tags: ['小股东'] },
        { keyNo: 'partner3', name: '股东3', tags: ['大股东', '法人'] },
      ];

      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockPersonHelper.getPartnerList).toHaveBeenCalledWith('test-company-id', 'all');
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                { terms: { KeyNo: ['partner1', 'partner3'] } }, // 只包含大股东
              ]),
            }),
          }),
        }),
        'test-company-id',
      );
    });

    it('应该处理没有大股东的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.MajorShareholder]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockPartners = [
        { keyNo: 'partner1', name: '股东1', tags: ['小股东'] },
        { keyNo: 'partner2', name: '股东2', tags: ['普通股东'] },
      ];

      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });
  });

  describe('多种关联方类型组合处理', () => {
    it('应该正确处理多种关联方类型', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController, RelatedTypeEnums.MajorShareholder]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      mockPersonHelper.getPartnerList.mockResolvedValue([{ keyNo: 'partner1', name: '股东1', tags: ['大股东'] }]);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith('test-company-id', false);
      expect(mockPersonHelper.getPartnerList).toHaveBeenCalledWith('test-company-id', 'all');
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                { terms: { KeyNo: ['person1', 'partner1'] } }, // 包含所有类型的关联方
              ]),
            }),
          }),
        }),
        'test-company-id',
      );
    });
  });
});
