import { Test, TestingModule } from '@nestjs/testing';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitDetailProcessor } from '../../dimension-hit-detail.processor';
import { CaseReasonHelper } from '../../helper/case-reason.helper';
import { RelatedDimensionHitDetailProcessor } from '../../related-dimension-hit-detail.processor';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createMockStrategyField = (
  fieldKey: DimensionFieldKeyEnums,
  fieldValue: any[],
  compareType?: DimensionFieldCompareTypeEnums,
): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  field.compareType = compareType;
  return field;
};

describe('RelatedDimensionHitDetailProcessor 上市主体企业处理测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getFinalActualController: jest.fn(),
      getPartnerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getInvestCompany: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      commonCivilRiskChange: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      detailAnalyzeForRelated: jest.fn(),
      hitIndustryThresholdField: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      getCaseTitleDescData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn().mockResolvedValue({
      body: {
        hits: {
          total: { value: 0 },
          hits: [],
        },
      },
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('上市主体企业 (StockControlCompany) 处理', () => {
    it('应该处理A股上市企业（企业本身）', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.StockControlCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockCompanyInfo = {
        Tags: [
          {
            Type: 122,
            DataExtend2: JSON.stringify({ ListingStage: '1' }), // 已上市
          },
        ],
      } as any;

      mockCompanySearchService.companyDetailsQcc.mockResolvedValue(mockCompanyInfo);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockCompanySearchService.companyDetailsQcc).toHaveBeenCalledWith('test-company-id');
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                { terms: { KeyNo: ['test-company-id'] } }, // A股企业本身
              ]),
            }),
          }),
        }),
        'test-company-id',
      );
    });

    it('应该处理港股上市企业（从Tag30获取上市主体）', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.StockControlCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockCompanyInfo = {
        Tags: [
          {
            Type: 122,
            DataExtend2: JSON.stringify({ ListingStage: '1' }), // 已上市
          },
          {
            Type: 30, // 港股标签
            DataExtend2: JSON.stringify({ KN: 'hk-listed-company-id' }),
          },
        ],
      } as any;

      mockCompanySearchService.companyDetailsQcc.mockResolvedValue(mockCompanyInfo);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                { terms: { KeyNo: ['hk-listed-company-id'] } }, // 港股上市主体
              ]),
            }),
          }),
        }),
        'test-company-id',
      );
    });

    it('应该处理非上市企业', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.StockControlCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockCompanyInfo = {
        Tags: [
          {
            Type: 122,
            DataExtend2: JSON.stringify({ ListingStage: '0' }), // 未上市
          },
        ],
      } as any;

      mockCompanySearchService.companyDetailsQcc.mockResolvedValue(mockCompanyInfo);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });

    it('应该处理没有上市标签的企业', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.StockControlCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockCompanyInfo = {
        Tags: [], // 没有相关标签
      } as any;

      mockCompanySearchService.companyDetailsQcc.mockResolvedValue(mockCompanyInfo);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });

    it('应该处理企业信息为空的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.StockControlCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      mockCompanySearchService.companyDetailsQcc.mockResolvedValue(null);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });

    it('应该处理DataExtend2解析异常的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.StockControlCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockCompanyInfo = {
        Tags: [
          {
            Type: 122,
            DataExtend2: 'invalid-json', // 无效的JSON
          },
        ],
      } as any;

      mockCompanySearchService.companyDetailsQcc.mockResolvedValue(mockCompanyInfo);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      // JSON 解析异常应该被捕获，不会抛出错误，但也不会识别为上市企业
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });

    it('应该处理港股企业但没有KN字段的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.StockControlCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      const mockCompanyInfo = {
        Tags: [
          {
            Type: 122,
            DataExtend2: JSON.stringify({ ListingStage: '1' }), // 已上市
          },
          {
            Type: 30, // 港股标签
            DataExtend2: JSON.stringify({}), // 没有KN字段
          },
        ],
      } as any;

      mockCompanySearchService.companyDetailsQcc.mockResolvedValue(mockCompanyInfo);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });

    it('应该处理companyDetailsQcc抛出异常的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.StockControlCompany]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      mockCompanySearchService.companyDetailsQcc.mockRejectedValue(new Error('API调用失败'));

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act & Assert
      await expect(processor.fetchHits(dimension, params)).rejects.toThrow('API调用失败');
    });
  });
});
