import { Test, TestingModule } from '@nestjs/testing';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitDetailProcessor } from '../../dimension-hit-detail.processor';
import { CaseReasonHelper } from '../../helper/case-reason.helper';
import { RelatedDimensionHitDetailProcessor } from '../../related-dimension-hit-detail.processor';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

describe('RelatedDimensionHitDetailProcessor 核心功能测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getFinalActualController: jest.fn(),
      getPartnerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getInvestCompany: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      commonCivilRiskChange: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      detailAnalyzeForRelated: jest.fn(),
      hitIndustryThresholdField: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      getCaseTitleDescData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('构造函数和初始化', () => {
    it('应该正确创建实例', () => {
      expect(processor).toBeDefined();
      expect(processor).toBeInstanceOf(RelatedDimensionHitDetailProcessor);
    });

    it('应该正确注入所有依赖', () => {
      expect(processor['personHelper']).toBe(mockPersonHelper);
      expect(processor['companyDetailService']).toBe(mockCompanyDetailService);
      expect(processor['companySearchService']).toBe(mockCompanySearchService);
      expect(processor['riskChangeHitDetailAnalyzer']).toBe(mockRiskChangeHitDetailAnalyzer);
      expect(processor['riskChangeHelper']).toBe(mockRiskChangeHelper);
      expect(processor['caseReasonHelper']).toBe(mockCaseReasonHelper);
    });
  });

  describe('bindRiskChangeEsSearchFn 方法测试', () => {
    it('应该正确绑定ES搜索函数', () => {
      // Arrange
      const mockEsSearchFn = jest.fn();

      // Act
      processor.bindRiskChangeEsSearchFn(mockEsSearchFn);

      // Assert
      expect(processor.searchEs).toBe(mockEsSearchFn);
    });

    it('绑定后应该能够调用ES搜索函数', async () => {
      // Arrange
      const mockEsSearchFn = jest.fn().mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      });
      processor.bindRiskChangeEsSearchFn(mockEsSearchFn);

      // Act
      await processor.searchEs({ query: {} }, 'test-company');

      // Assert
      expect(mockEsSearchFn).toHaveBeenCalledWith({ query: {} }, 'test-company');
    });
  });

  describe('processAnalyze 方法测试', () => {
    beforeEach(() => {
      processor.bindRiskChangeEsSearchFn(mockSearchEs);
    });

    it('应该正确处理空的关联方维度列表', async () => {
      // Arrange
      const relatedRiskChangeDims: DimensionHitStrategyPO[] = [];
      const companyId = 'test-company-id';

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('应该正确处理单个维度且无命中记录', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      const relatedRiskChangeDims = [dimension];
      const companyId = 'test-company-id';

      // Mock fetchHits 返回无命中结果
      const mockFetchResponse = new HitDetailsBaseResponse();
      mockFetchResponse.Paging = {
        PageSize: 1,
        PageIndex: 1,
        TotalRecords: 0,
      };
      mockFetchResponse.Result = [];

      jest.spyOn(processor, 'fetchHits').mockResolvedValue(mockFetchResponse);

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toHaveLength(0);
      expect(processor.fetchHits).toHaveBeenCalledWith(dimension, {
        keyNo: companyId,
        pageIndex: 1,
        pageSize: 1,
      });
    });

    it('应该正确处理有命中记录的维度', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension.strategyName = '实际控制人风险变更测试';
      dimension.template = '匹配到目标主体 <em class="#isHidden#">【#name#】</em>，共命中 #count# 条记录';

      const relatedRiskChangeDims = [dimension];
      const companyId = 'test-company-id';

      // Mock fetchHits 返回有命中结果
      const mockFetchResponse = new HitDetailsBaseResponse();
      mockFetchResponse.Paging = {
        PageSize: 1,
        PageIndex: 1,
        TotalRecords: 3,
      };
      mockFetchResponse.Result = [
        { id: 'result1', companyName: '关联公司1' },
        { id: 'result2', companyName: '关联公司2' },
        { id: 'result3', companyName: '关联公司3' },
      ];

      jest.spyOn(processor, 'fetchHits').mockResolvedValue(mockFetchResponse);

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(DimensionHitResultPO);
      expect(result[0].dimensionKey).toBe(DimensionTypeEnums.ActualControllerRiskChange);
      expect(result[0].totalHits).toBe(3);
      expect(result[0].description).toContain('实际控制人风险变更测试');
      expect(result[0].description).toContain('3');
    });

    it('应该正确处理多个维度的混合情况', async () => {
      // Arrange
      const dimension1 = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      dimension1.strategyName = '实际控制人风险变更';
      dimension1.template = '匹配到目标主体 <em class="#isHidden#">【#name#】</em>，共命中 #count# 条记录';

      const dimension2 = createTestDimensionStrategy(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
      dimension2.strategyName = '近期对外投资企业大量注销或吊销';
      dimension2.template = '匹配到目标主体 <em class="#isHidden#">【#name#】</em>，共命中 #count# 条记录';

      const relatedRiskChangeDims = [dimension1, dimension2];
      const companyId = 'test-company-id';

      // Mock fetchHits 返回不同结果
      const mockFetchResponse1 = new HitDetailsBaseResponse();
      mockFetchResponse1.Paging = { PageSize: 1, PageIndex: 1, TotalRecords: 2 };
      mockFetchResponse1.Result = [{ id: 'result1' }, { id: 'result2' }];

      const mockFetchResponse2 = new HitDetailsBaseResponse();
      mockFetchResponse2.Paging = { PageSize: 1, PageIndex: 1, TotalRecords: 0 };
      mockFetchResponse2.Result = [];

      jest.spyOn(processor, 'fetchHits').mockResolvedValueOnce(mockFetchResponse1).mockResolvedValueOnce(mockFetchResponse2);

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toHaveLength(1); // 只有第一个维度有命中
      expect(result[0].dimensionKey).toBe(DimensionTypeEnums.ActualControllerRiskChange);
      expect(result[0].totalHits).toBe(2);
      expect(processor.fetchHits).toHaveBeenCalledTimes(2);
    });

    it('应该正确处理fetchHits抛出异常的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);
      const relatedRiskChangeDims = [dimension];
      const companyId = 'test-company-id';

      jest.spyOn(processor, 'fetchHits').mockRejectedValue(new Error('ES查询失败'));

      // Act & Assert
      await expect(processor.processAnalyze(relatedRiskChangeDims, companyId)).rejects.toThrow('ES查询失败');
    });
  });
});
