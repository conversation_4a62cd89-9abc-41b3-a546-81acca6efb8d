import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { Injectable } from '@nestjs/common';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { getRiskListDesc } from 'apps/data/risk.copy.from.c/risk';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { getCompareResult } from 'libs/utils/diligence/diligence.utils';
import { orderBy } from 'lodash';
import { PersonHelper } from '../../helper/person.helper';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { CaseReasonHelper } from './helper/case-reason.helper';
import { DimensionHitResultPO } from '../../../../libs/model/diligence/dimension/DimensionHitResultPO';
import * as Bluebird from 'bluebird';
import { getDimensionDescription, processDimHitResPO } from '../../../../libs/utils/diligence/dimension.utils';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response/HitDetailsBaseResponse';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DimensionHelper } from '../../helper/dimension.helper';

@Injectable()
export class RelatedDimensionHitDetailProcessor {
  private readonly logger: Logger = QccLogger.getLogger(RelatedDimensionHitDetailProcessor.name);

  searchEs: (body: any, companyId: string) => Promise<any>;

  constructor(
    private readonly personHelper: PersonHelper,
    private readonly companyDetailService: CompanyDetailService,
    private readonly riskChangeHelper: RiskChangeHelper,
    private readonly caseReasonHelper: CaseReasonHelper,
    private readonly dimensionHelper: DimensionHelper,
  ) {}

  bindRiskChangeEsSearchFn(esSearchFn: (body: any, companyId: string) => Promise<any>) {
    this.searchEs = esSearchFn;
  }

  /**
   * 处理关联方维度的 analyze
   */
  async processAnalyze(relatedRiskChangeDims: DimensionHitStrategyPO[], companyId: string) {
    const analyzeResult: DimensionHitResultPO[] = [];
    if (relatedRiskChangeDims.length > 0) {
      await Bluebird.map(relatedRiskChangeDims, async (rilatedDimHit: DimensionHitStrategyPO) => {
        const res = await this.fetchHits(rilatedDimHit, {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 1,
        });
        const desData = {
          isHidden: '',
          isHiddenY: '',
        };
        // 再过滤命中记录条数
        let hitCount = 0;
        if (res?.Paging.TotalRecords) {
          hitCount = res?.Paging.TotalRecords || 0;
        }
        // 再过滤命中记录条数
        const hitCountField = rilatedDimHit.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          hitCount = 0;
        }
        if (hitCount > 0) {
          const dimHit = processDimHitResPO(rilatedDimHit, hitCount, desData);
          if (rilatedDimHit.template && desData) {
            // 返回描述处理
            desData['name'] = rilatedDimHit.strategyName;
            dimHit.description = getDimensionDescription(rilatedDimHit.template, Object.assign(desData, { count: hitCount }));
          }
          analyzeResult.push(dimHit);
        }
      });
    }
    return analyzeResult;
  }

  /**
   * 关联方企业的动态判断
   *  维度： 近期对外投资企业大量注销或吊销 RecentInvestCancellations
   *  维度： 实控人风险动态  ActualControllerRiskChange
   * @param dimension
   * @param params
   */
  @TraceLog({ throwError: true, spanType: 3, spanName: 'getRelatedComapnyRiskChange' })
  public async fetchHits(dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const { keyNo: companyId, pageIndex, pageSize } = params;
    const response = new HitDetailsBaseResponse();
    response.Paging = {
      PageSize: pageSize,
      PageIndex: pageIndex,
      TotalRecords: 0,
    };
    // 指定查询关联方风险
    let riskCategories = [];

    // 查询范围，如果指定查询关联方风险 获取关联方范围定义
    let companyIds = [];
    const relatedRoleType = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType);
    if (relatedRoleType?.fieldValue.length) {
      companyIds = await this.dimensionHelper.getRelatedRoleType(dimension, companyId);
    }

    // 如果没有指定查询关联方风险，返回空
    if (!companyIds.length) {
      return response;
    }

    const query = { bool: { filter: [] } };
    query.bool.filter.push({ range: { Es_Version: { lt: 999999 } } });
    query.bool.filter.push({ terms: { KeyNo: companyIds } });

    const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
    if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
      query.bool.filter.push({ term: { IsValid: Number(isValidParams.fieldValue[0]) } });
    }

    const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
    if (riskCategoriesParams) {
      riskCategories = riskCategoriesParams.fieldValue;
      query.bool.filter.push({ terms: { Category: riskCategories } });
    } else {
      return response;
    }

    // 查询监控单位时间内的动态数据
    const dimensionFilter = dimension?.dimensionFilter;
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      // 关联方或者实际控制人也需要满足当前监控单位时间内产生动态，才会触发关联方的动态信息
      const range = {
        CreateDate: {
          gte: Math.ceil(dimensionFilter?.startTime),
          lte: Math.ceil(dimensionFilter?.endTime),
        },
      };
      query.bool.filter.push({ range });
    }
    // 传动态Id
    if (dimensionFilter?.id) {
      query.bool.filter.push({ term: { Id: dimensionFilter.id } });
    }

    const sort = {};
    const dimSortFiled = dimension.getSortField();
    if (params?.field) {
      sort[params.field] = params?.order || 'DESC';
    } else if (dimSortFiled?.field) {
      sort[dimSortFiled.field] = dimSortFiled.order;
    }

    const esResult = await this.searchEs({ from: 0, size: 1000, sort, query }, companyId);
    // 监控周期都有相应的动态发生
    const queryResult = {
      total: esResult?.body?.hits?.total?.value || 0,
      data: esResult?.body?.hits?.hits?.map((d) => d._source) || [],
    };

    let isHit = true;
    /**  符合条件的动态记录数组 */
    const hitData: any[] = [];
    if (queryResult.total === 0) {
      return response;
    }
    switch (dimension.key) {
      case DimensionTypeEnums.RecentInvestCancellationsRiskChange: {
        // 对外投资企业有注销吊销动态发生
        let periodHitDate: any[] = [];
        let subHitData: any[] = [];
        const timePeriodField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.timePeriod);
        if (timePeriodField && isHit) {
          isHit = false;
          periodHitDate = (await this.riskChangeHelper.commonCivilRiskChange(companyIds, riskCategories, timePeriodField.fieldValue[0], 'month', 10000))
            ?.Result;
          if (periodHitDate?.length) {
            isHit = true;
          }
        }
        // 通用的关联方过滤
        if (isHit) {
          isHit = false;
          if (!periodHitDate?.length) {
            periodHitDate.push(...queryResult.data);
          }
          subHitData = await this.riskChangeHelper.detailAnalyzeForRelated(periodHitDate, dimension, params);
          if (subHitData?.length) {
            isHit = true;
          }
        }
        let investCompanyCount = null;
        const fundedRatioLevelField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.fundedRatioLevel);
        if (fundedRatioLevelField && isHit) {
          isHit = false;
          // 投资企业的持股比例
          const fundedRatioLevel = fundedRatioLevelField?.fieldValue?.[0] || 0;
          // 验证是否满足命中阈值
          const { Paging } = await this.companyDetailService.getInvestCompany(companyId, fundedRatioLevel);
          investCompanyCount = Paging.TotalRecords;
          if (investCompanyCount > 0) {
            isHit = true;
          }
        }
        const thresholdRuleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.thresholdRule);
        if (thresholdRuleField && isHit) {
          isHit = false;
          thresholdRuleField.fieldValue.forEach((rule) => {
            if (getCompareResult(investCompanyCount, rule.investCount[0], DimensionFieldCompareTypeEnums.Between, rule.investCount[1])) {
              // 近期对外投资企业大量注销或吊销的数量 / 对外投资企业数量 >= 阈值%
              isHit = subHitData.length / investCompanyCount >= rule.threshold / 100;
            }
          });
        }
        const hitCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && isHit) {
          isHit = false;
          const compareType = hitCountField?.compareType;
          isHit = getCompareResult(investCompanyCount, hitCountField?.fieldValue?.[0], compareType);
        }
        if (isHit) {
          hitData.push(...subHitData);
        }
        break;
      }
      case DimensionTypeEnums.ActualControllerRiskChange: {
        // 实控人近x个月内新增或减少控股子公司数量超过xx个
        let periodHitDate: any[] = [];
        let subHitData: any[] = [];
        const timePeriodField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.timePeriod);
        if (timePeriodField && isHit) {
          // 指定查询仅xx月, 实控人产生的动态
          isHit = false;
          periodHitDate = (await this.riskChangeHelper.commonCivilRiskChange(companyIds, riskCategories, timePeriodField.fieldValue[0], 'month', 10000)).Result;
          if (periodHitDate?.length) {
            isHit = true;
          }
        }
        // 通用的关联方过滤
        if (isHit) {
          isHit = false;
          if (!periodHitDate?.length) {
            periodHitDate.push(...queryResult.data);
          }
          subHitData = await this.riskChangeHelper.detailAnalyzeForRelated(periodHitDate, dimension, params);
          if (subHitData?.length) {
            isHit = true;
          }
        }
        // 验证是否满足命中阈值
        const thresholdCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.thresholdCount);
        if (thresholdCountField && isHit) {
          isHit = false;
          const thresholdCount = thresholdCountField?.fieldValue?.[0] || 0;
          const compareType = thresholdCountField?.compareType || DimensionFieldCompareTypeEnums.GreaterThan;
          if (subHitData?.length && null !== thresholdCount && getCompareResult(subHitData.length, thresholdCount, compareType)) {
            isHit = true;
          }
        }
        const industryThresholdField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.industryThreshold);
        if (industryThresholdField && isHit) {
          isHit = false;
          if (subHitData?.length && (await this.riskChangeHelper.hitIndustryThresholdField(industryThresholdField, subHitData, params.keyNo))) {
            isHit = true;
          }
        }
        if (isHit) {
          hitData.push(...subHitData);
        }
        break;
      }
      default: {
        let subHitData: any[] = [];
        // 通用的关联方过滤
        if (isHit) {
          isHit = false;
          subHitData = await this.riskChangeHelper.detailAnalyzeForRelated(queryResult.data, dimension, params);
          if (subHitData?.length) {
            isHit = true;
          }
        }
        if (isHit) {
          hitData.push(...subHitData);
        }
        break;
      }
    }

    if (isHit) {
      response.Paging.TotalRecords = hitData.length;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      response.Result = sortedData.slice(start, end);
      if (response.Result.length > 0) {
        const changeExtendMap = {};
        response.Result.forEach((item) => {
          const { ChangeExtend, ObjectId } = item;
          if (!changeExtendMap[ObjectId]) {
            changeExtendMap[ObjectId] = [];
          }
          changeExtendMap[ObjectId].push(ChangeExtend);
        });
        // 返回数据结构处理
        response.Result = response.Result.map((d) => getRiskListDesc(d));
        await this.caseReasonHelper.getCaseTitleDescData(response.Result, false, changeExtendMap);
      }
    }
    return response;
  }
}
