import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Cacheable } from '@type-cacheable/core';
import { PersonData } from '../../../libs/model/data/source/PersonData';
import { find, flatten, some } from 'lodash';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import * as Bluebird from 'bluebird';
import { PersonHelper } from './person.helper';
import { TargetInvestigationEnums } from '../../../libs/enums/dimension/FieldValueEnums';
import { RelatedHelper } from './related.helper';
import { CompanyDetailService } from '../../company/company-detail.service';
import { RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { CompanySearchService } from '../../company/company-search.service';

@Injectable()
export class DimensionHelper {
  public readonly logger: Logger = QccLogger.getLogger(DimensionHelper.name);

  constructor(
    private readonly personHelper: PersonHelper,
    private readonly relatedHelper: RelatedHelper,
    private readonly companyDetailService: CompanyDetailService,
    private readonly companySearchService: CompanySearchService,
  ) {}

  /**
   * 监控动态用，排查主体 获取主体关联的人员 (目前只用于
   *   DimensionTypeEnums.RecentInvestCancellationsRiskChange,
   *   DimensionTypeEnums.ActualControllerRiskChange,
   *   DimensionTypeEnums.ListedEntityRiskChange,
   *   DimensionTypeEnums.RiskChange)，后期再做合并
   * @param dimension
   * @param companyId
   */
  public async getRelatedRoleType(dimension: DimensionHitStrategyPO, companyId: string): Promise<string[]> {
    // 查询范围，如果指定查询关联方风险 获取关联方范围定义
    const relatedRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.relatedRoleType);
    const companyIds = [];
    if (relatedRoleField) {
      // 对外投资企业
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.InvestCompany)) {
        // 投资企业的状态
        const compnayStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessStatus);
        const status = compnayStatusField?.fieldValue;
        // 投资企业的持股比例
        const fundedRatioLevelField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.fundedRatioLevel);
        const fundedRatioLevel = fundedRatioLevelField?.fieldValue?.[0] || 0;
        // 符合条件的对外投资企业列表
        const { Paging, Result } = await this.companyDetailService.getInvestCompany(companyId, fundedRatioLevel, status, 200);
        if (Paging?.TotalRecords > 0) {
          companyIds.push(...Result.map((d) => d.KeyNo));
        }
      }
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.ActualController)) {
        // 实际控制人
        const personlist = await this.personHelper.getFinalActualController(companyId, false);
        const keyNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
        if (keyNos?.length) {
          companyIds.push(...keyNos);
        }
      }
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.MajorShareholder)) {
        // 大股东
        const partnerList = await this.personHelper.getPartnerList(companyId, 'all');
        const bigStockers = partnerList.filter((partner) => partner?.tags.includes('大股东'));
        if (bigStockers?.length) {
          const keyNos = bigStockers?.length ? bigStockers?.map((p) => p.keyNo).filter((t) => t) : [];
          if (keyNos?.length) {
            companyIds.push(...keyNos);
          }
        }
      }
      if (relatedRoleField.fieldValue?.includes(RelatedTypeEnums.StockControlCompany)) {
        // 上市主体企业， A股的上市主体是企业本身，港股的上市主体在企业详情的Tag中获取
        let isListCompany = false;
        const companyInfo = await this.companySearchService.companyDetailsQcc(companyId);
        if (companyInfo?.Tags?.length) {
          // 是否是上市企业
          const Tag122 = companyInfo?.Tags?.find((t) => t.Type === 122);
          if (Tag122) {
            try {
              const dataExtend2 = JSON.parse(Tag122?.DataExtend2 || '{}');
              const ListingStage = dataExtend2?.ListingStage;
              if (ListingStage === '1') {
                isListCompany = true;
              }
            } catch (error) {
              this.logger.error(`数据解析失败,companyInfo =${companyInfo}，错误: ${error.message}`);
            }
          }
          // 如果是上市企业
          if (isListCompany) {
            // 港股企业
            const Tag30 = companyInfo?.Tags?.find((t) => t.Type === 30);
            if (Tag30) {
              try {
                const dataExtend2 = JSON.parse(Tag30?.DataExtend2 || '{}');
                const companyKeyNo = dataExtend2?.KN;
                if (companyKeyNo) {
                  companyIds.push(companyKeyNo);
                }
              } catch (error) {
                this.logger.error(`数据解析失败,companyInfo =${companyInfo}，错误: ${error.message}`);
              }
            } else {
              companyIds.push(companyId);
            }
          }
        }
      }
    }
    return companyIds;
  }

  /**
   * 排查主体 获取主体关联的人员
   * @param targetInvestigation
   * @param companyId
   */
  public async getTargetInvestigation(dimension: DimensionHitStrategyPO, companyId: string): Promise<string[]> {
    const ids = [];
    const targetInvestigationField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation);
    if (targetInvestigationField) {
      const targetValue = targetInvestigationField.fieldValue;
      if (targetValue.includes(TargetInvestigationEnums.Self)) {
        ids.push(companyId);
      }
      if (targetValue.includes(TargetInvestigationEnums.Legal)) {
        const personlist = await this.personHelper.getLegalPerson(companyId);
        const personNos = personlist.map((p) => p.keyNo).filter((t) => t);
        if (personNos?.length) {
          ids.push(...personNos);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.HisLegal)) {
        const personlist = await this.personHelper.getHisLegalPerson(companyId);
        const personNos = personlist.map((p) => p.keyNo).filter((t) => t);
        if (personNos?.length) {
          ids.push(...personNos);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.MainStaff)) {
        const personlist = await this.personHelper.getEmployeeList(companyId);
        const personNos = personlist.map((p) => p.keyNo).filter((t) => t);
        if (personNos?.length) {
          ids.push(...personNos);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.HisMainStaff)) {
        const personlist = await this.personHelper.getHisEmployeeData(companyId);
        const personNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
        if (personNos?.length) {
          ids.push(...personNos);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.Branch)) {
        const companyBranches = await this.companyDetailService.getBranchKeyNos(companyId);
        if (companyBranches?.length) {
          ids.push(...companyBranches);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.ActualController)) {
        const personlist = await this.personHelper.getFinalActualController(companyId);
        const personNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
        if (personNos?.length) {
          ids.push(...personNos);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.Benefit)) {
        const personlist = await this.personHelper.getBenefitList(companyId, false);
        const personNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
        if (personNos?.length) {
          ids.push(...personNos);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.MajorityHoldingCompanies)) {
        const result = await this.companyDetailService.getMajorityHeldCompanies(companyId);
        const keyNos = result?.holdingList?.length > 0 ? result?.holdingList?.filter((x) => x.KeyNo).map((x) => x.KeyNo) : [];
        if (keyNos?.length) {
          ids.push(...keyNos);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.IcbcSFRelated)) {
        const result = await this.relatedHelper.getIcbcSFRelateds(companyId);
        const keyNos = result?.length > 0 ? result?.map((x) => x.companyKeynoRelated) : [];
        if (keyNos?.length) {
          ids.push(...keyNos);
        }
      }
      if (targetValue.includes(TargetInvestigationEnums.LargestShareholder)) {
        const partnerData = await this.personHelper.getPartnerList(companyId, 'all');
        const keyNos = partnerData?.filter((p) => !!p.keyNo && p.tags?.includes('大股东')).map((p) => p.keyNo);
        if (keyNos?.length) {
          ids.push(...keyNos);
        }
      }
    }
    return ids;
  }

  /**
   * 获取关联企业以及人员
   * @param companyId
   * @param associateObjects 关联对象
   * @param associateExcludes 关联排除
   * @private
   */
  @Cacheable({ ttlSeconds: 100 })
  public async getRelatedCompanyAndPerson(companyId: string, associateObjects: any[], associateExcludes: any[]): Promise<PersonData[]> {
    const investExcludeCompanyIds = [
      '715f4a0a608f1e6d4a772c31ade1b171', //梧桐树 投资平台有限责任公司
      'he7910e63f79702ea812d50f082dfaec', //香港中央結算有限公司
      'h1a3ef7ad8ec2e56ba43e250225d1d28', //香港中央結算（代理人）有限公司
      'g599bafb4da74aa069cc9b67a39992e0', //全国社会保障基金理事会
      'a0ed6d61d21271179021367cac5d4420', //国开发展基金有限公司
      'b2fbc4155d4781978f94ef67bc61c1e7', //国开基础设施基金有限公司
      'ga7b26dace8da0c3b2fbdc6acefb4739', //国务院
      'g38ed9b86b70ab7a169f5ba36ab4bfb9', //财政部
      'g4bdd6c24056bb434978acfc5f40d168', //国资委
    ];
    //投资机构Tags
    const investTags = ['私募基金', '投资', '基金', '资产管理', '私募', '资本', '城投', '证券', '保险', '信托', '银行', '财富关联'];
    const promiseArr = [this.personHelper.getEmployeeList(companyId)];
    const isLegal = find(associateObjects, { key: DetailsParamEnums.LegalRepresentative })?.status == 1;
    const isActualControl = find(associateObjects, { key: DetailsParamEnums.ActualController })?.status == 1;
    const isMajorShareholder = find(associateObjects, { key: DetailsParamEnums.MajorShareholder })?.status == 1;
    const isShareholder = find(associateObjects, { key: DetailsParamEnums.Shareholder })?.status == 1;
    const ignoreInvest = associateExcludes ? find(associateExcludes, { key: DetailsParamEnums.ShareHolderInvest })?.status == 1 : false;
    if (isLegal) {
      promiseArr.push(this.personHelper.getLegalPerson(companyId));
    }
    if (isActualControl) {
      promiseArr.push(this.personHelper.getFinalActualController(companyId));
    }
    if (isShareholder || isMajorShareholder) {
      promiseArr.push(this.personHelper.getPartnerList(companyId, 'all', ignoreInvest));
    }
    let personData: PersonData[] = flatten(await Bluebird.all(promiseArr));
    if (isShareholder || isMajorShareholder) {
      //股东包含大股东，如果只包含大股东，则只显示大股东
      if (!isShareholder && isMajorShareholder) {
        personData = personData.filter((x) => x.tags.includes('大股东'));
      }
    }
    const keyNosSet: Set<string> = new Set<string>();
    const uniqPersonData = [];
    personData
      .filter((x) => x?.keyNo)
      .forEach((x) => {
        if (!keyNosSet.has(x.keyNo)) {
          if (
            ignoreInvest &&
            (investExcludeCompanyIds.includes(x.keyNo) ||
              some(x.tags, (t) => investTags.includes(t)) ||
              x.keyNo.startsWith('g') ||
              some(x.name, (t) => investTags.includes(t)))
          ) {
            //如果关联派出设置了排除股东类型为投资机构，则排除投资机构，keyNo g开头的是机关单位，在本需求中也当投资机构处理
            return;
          }
          uniqPersonData.push(x);
          keyNosSet.add(x.keyNo);
        }
      });
    return uniqPersonData;
  }
}
