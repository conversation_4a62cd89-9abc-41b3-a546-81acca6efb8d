import { EnterpriseLibApiSource } from './source/enterprise-lib-api.source';
import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../app/app.test.module';
import { DataModule } from './data.module';
import { getDimensionHitStrategyPO } from '../test_utils_module/dimension.test.utils';
import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../libs/enums/dimension/dimension.filter.params';
import {
  IsInstitutionalInvestorConstant,
  IsStateOwnedConstant,
  OutwardInvestmentStatisticsConstant,
  PatentIsTransferConstant,
  PatentStatusConstant,
  PatentTypeConstant,
  ShareholdRoleConstant,
} from '../../libs/constants/company.constants';
import { DimensionFieldCompareTypeEnums } from '../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { HitDetailsBaseQueryParams } from '../../libs/model/diligence/details/request';
import { RelatedTypeEnums } from '../../libs/enums/dimension/RelatedTypeEnums';
import { CompanyApiSource } from './source/company-api.source';
import { HolderRoleType, IsPEVCMap, LayTypeMap, RealRegistrationErrorMap, ShareChangeStatusMap } from '../../libs/constants/risk.change.constants';
import { RiskChangeEsSource } from './source/risk-change/risk-change-es.source';
import { TargetInvestigationEnums } from '../../libs/enums/dimension/FieldValueEnums';
import { CreditEsSource } from './source/credit-es.source';
import { JudgementSource } from './source/judgement.source';
import { AssertESSource } from './source/asset-es.source';
import { PersonCaseTargetInvestigation } from '../../libs/constants/judgement.constants';
import { SupervisePunishEsSource } from './source/supervise-punish-es.source';

jest.setTimeout(60 * 10000);
describe('icbc贷前尽调指标集成测试', () => {
  let enterpriseLibService: EnterpriseLibApiSource;
  let companyApiSource: CompanyApiSource;
  let riskChangeEsSource: RiskChangeEsSource;
  let creditEsService: CreditEsSource;
  let judgementService: JudgementSource;
  let assertESService: AssertESSource;
  let supervisePunishService: SupervisePunishEsSource;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    enterpriseLibService = module.get<EnterpriseLibApiSource>(EnterpriseLibApiSource);
    companyApiSource = module.get<CompanyApiSource>(CompanyApiSource);
    riskChangeEsSource = module.get<RiskChangeEsSource>(RiskChangeEsSource);
    creditEsService = module.get<CreditEsSource>(CreditEsSource);
    judgementService = module.get<JudgementSource>(JudgementSource);
    assertESService = module.get<AssertESSource>(AssertESSource);
    supervisePunishService = module.get<SupervisePunishEsSource>(SupervisePunishEsSource);
  });

  it('【第一大股东持股比例】-第一大股东持股比例', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ShareholderInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareholdRole,
        fieldValue: ['majorShareholder'],
        options: ShareholdRoleConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'stockpercent', order: 'DESC', fieldSnapshot: 'StockPercent' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人控股比例比例】-实控人控股比例比例', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActuralControllerInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人控制年限】-实控人控制年限', async () => {
    const companyId = '6583579d4d007d7daab586c94d592e93';
    const companyName = '优合集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActuralControllerInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.controllerTime,
        fieldValue: [0],
        options: [{ unit: '月', min: 0, max: 9999 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人名称、是否国资委】-实控人名称、是否国资委', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActuralControllerInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.isStateOwned,
        fieldValue: [2],
        options: IsStateOwnedConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人控股公司总数量】-实控人控股公司总数量', async () => {
    const companyId = 'c406abaf3e36d173186aa5e99d506fbd';
    const companyName = '华润股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ControllerCompany, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        fieldValue: [50],
        accessScope: 2,
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业经营状态】-在业/存续/迁入/迁出/注销/吊销/撤销/清算/歇业/除名/责令关闭', async () => {
    const companyId = '430d0eda06346e9d9b6e44dc4b4e8a91';
    const companyName = '91440300342841643F';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        fieldValue: ['10', '20', '40', '90', '85', '70', '99', '80', '75', '50', '60', '87'],
        accessScope: 2,
        options: [
          { label: '在业', value: '10' },
          { label: '存续', value: '20' },
          { label: '清算', value: '40' },
          { label: '吊销', value: '90' },
          { label: '责令关闭', value: '85' },
          { label: '停业', value: '70' },
          { label: '注销', value: '99' },
          { label: '撤销', value: '80' },
          { label: '歇业', value: '75' },
          { label: '迁入', value: '50' },
          { label: '迁出', value: '60' },
          { label: '除名', value: '87' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业成立天数】-企业成立天数', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.duration,
        fieldValue: [30],
        options: [{ unit: '月', min: 0, max: 9999 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【法人代表变更】-近5年法代变更次数', async () => {
    const companyId = '3f9aafffc19676e1e4676ee87c8d85ee';
    const companyName = '邵阳市东石科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [72],
        options: [{ value: 72, label: '成员变更' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.layTypes,
        fieldValue: [1],
        options: LayTypeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeEsSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await riskChangeEsSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【近5年其他高管变更次数】-近5年其他高管变更次数', async () => {
    const companyId = '33393e8033dc119efa2fd45a38d50bfc';
    const companyName = '上海联银创业投资有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [72],
        options: [{ value: 72, label: '成员变更' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.layTypes,
        fieldValue: [1],
        options: LayTypeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
    ]);
    const detail = await riskChangeEsSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await riskChangeEsSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业所属行业-国标行业】- 房地产开发经营', async () => {
    const companyId = '08d510c2ca7cdeee72ec147dd8fec1ec';
    const companyName = '河北中天房地产开发集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyIndustry,
        fieldValue: ['K-70-701'],
        accessScope: 2,
        options: [{ value: 'K-70-701', label: '房地产开发经营' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业所属行业-企查查行业】- 房地产开发经营', async () => {
    const companyId = '08d510c2ca7cdeee72ec147dd8fec1ec';
    const companyName = '河北中天房地产开发集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.qccIndustry,
        fieldValue: ['28-2801'],
        accessScope: 2,
        options: [{ value: '28-2801', label: '房地产开发' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【境内上市公司】- 境内上市公司(非ST/*ST)', async () => {
    const companyId = '290e1083a754b6f47210639397bf6934';
    const companyName = '科沃斯机器人股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyListed,
        fieldValue: [3],
        options: [{ label: '上市公司(非ST/*ST)', value: 3 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.listedIndustry,
        fieldValue: ['S_10101', 'S_10401', 'S_10201', 'S_10301', 'S_10501'],
        options: [
          { label: '上交所主板', value: 'S_10101' },
          { label: '上交所科创板', value: 'S_10401' },
          { label: '深交所主板', value: 'S_10201' },
          { label: '深交所创业板', value: 'S_10301' },
          { label: '北交所', value: 'S_10501' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  /* it('【境内上市公司】- 关联方境内上市公司(非ST/!*ST)', async () => {
    const companyId = '290e1083a754b6f47210639397bf6934';
    const companyName = '科沃斯机器人股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyListed,
        fieldValue: [3],
        options: [{ label: '上市公司(非ST/!*ST)', value: 3 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.listedIndustry,
        fieldValue: ['S_10101', 'S_10401', 'S_10201', 'S_10301', 'S_10501'],
        options: [
          { label: '上交所主板', value: 'S_10101' },
          { label: '上交所科创板', value: 'S_10401' },
          { label: '深交所主板', value: 'S_10201' },
          { label: '深交所创业板', value: 'S_10301' },
          { label: '北交所', value: 'S_10501' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });*/

  it('【企业的授权发明专利数量】- 授权发明专利数量', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentInfo, [
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentStatus,
        fieldValue: ['ZT002001'],
        options: PatentStatusConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业近三年有授权发明专利】- 近三年有授权发明专利数量', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentInfo, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [1],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentStatus,
        fieldValue: ['ZT002001'],
        options: PatentStatusConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业被转让的授权发明专利数量】- 被转让的授权发明专利数量', async () => {
    const companyId = '6b242b475738f45a4dd180564d029aa9';
    const companyName = '华为技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PatentInfo, [
      {
        fieldKey: DimensionFieldKeyEnums.patentType,
        fieldValue: ['1', '2'],
        options: PatentTypeConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentIsTransfer,
        fieldValue: [1],
        options: PatentIsTransferConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.patentStatus,
        fieldValue: ['ZT002001'],
        options: PatentStatusConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业控股子公司总数量】- 企业控股子公司总数量', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        fieldValue: ['10', '20'],
        accessScope: 2,
        options: [
          { label: '在业', value: '10' },
          { label: '存续', value: '20' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【近一年企业控股子公司新增成立数量】- 近一年企业控股子公司总数量', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.registerDate,
        fieldValue: [365],
        accessScope: 2,
        options: [{ unit: '天', min: 1, max: 365 }],
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        fieldValue: ['10', '20'],
        accessScope: 2,
        options: [
          { label: '在业', value: '10' },
          { label: '存续', value: '20' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【对外投资企业涉房】- 企业控股子公司经营范围涉房', async () => {
    const companyId = '3946cc39c6d3ce74076d9bf063171cb4';
    const companyName = '芜湖信京投资中心（有限合伙）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companySocpe,
        fieldValue: ['土地开发', '地产开发', '商品房销售', '房地产项目投资'],
        accessScope: 2,
        options: [
          { value: '土地开发', label: '土地开发' },
          { value: '地产开发', label: '地产开发' },
          { value: '商品房销售', label: '商品房销售' },
          { value: '房地产项目投资', label: '房地产项目投资' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【对外投资企业涉房】- 企业控股子公司-国标行业涉房', async () => {
    const companyId = '3946cc39c6d3ce74076d9bf063171cb4';
    const companyName = '芜湖信京投资中心（有限合伙）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyIndustry,
        fieldValue: ['K-70-701'],
        accessScope: 2,
        options: [{ value: 'K-70-701', label: '房地产开发经营' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【对外投资企业涉房】- 企业控股子公司-企查查行业涉房', async () => {
    const companyId = '3946cc39c6d3ce74076d9bf063171cb4';
    const companyName = '芜湖信京投资中心（有限合伙）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.qccIndustry,
        fieldValue: ['28-2801'],
        accessScope: 2,
        options: [{ value: '28-2801', label: '房地产开发' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【对外投资企业涉金融】- 企业控股子公司名称涉金融', async () => {
    const companyId = '11fd3c6aa524b90d61c77ec6d8cf2327';
    const companyName = '深圳赛博福纳资本投资管理中心（有限合伙）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.excludeCompanyName,
        fieldValue: ['有限合伙'],
        options: [{ value: '有限合伙', label: '有限合伙' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyName,
        fieldValue: ['小额贷款', '互联网金融', '典当', '保理', '担保', '融资租赁'],
        accessScope: 2,
        options: [
          { value: '小额贷款', label: '小额贷款' },
          { value: '互联网金融', label: '互联网金融' },
          { value: '典当', label: '典当' },
          { value: '保理', label: '保理' },
          { value: '担保', label: '担保' },
          { value: '融资租赁', label: '融资租赁' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【近一年企业对外投资企业注销占比】- 近一年企业对外投资企业注销占比', async () => {
    const companyId = '1edc727b095d5ca493872d289c146091';
    const companyName = '上海元恒益新材料合伙企业（有限合伙）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestmentAnalysis, [
      {
        fieldKey: DimensionFieldKeyEnums.outwardInvestmentStatistics,
        fieldValue: [1],
        options: OutwardInvestmentStatisticsConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.registerDate,
        fieldValue: [365],
        accessScope: 2,
        options: [{ unit: '天', min: 1, max: 365 }],
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.leftRatio,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【境内上市公司】-关联方境内上市公司(非ST/*ST)', async () => {
    const companyId = '4ac0772e35cd8f2f514344b810f0d265';
    const companyName = '苏州工业园区泰怡凯电器有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyListed,
        fieldValue: [3],
        options: [{ label: '上市公司(非ST/*ST)', value: 3 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.listedIndustry,
        fieldValue: ['S_10101', 'S_10401', 'S_10201', 'S_10301', 'S_10501'],
        options: [
          { label: '上交所主板', value: 'S_10101' },
          { label: '上交所科创板', value: 'S_10401' },
          { label: '深交所主板', value: 'S_10201' },
          { label: '深交所创业板', value: 'S_10301' },
          { label: '北交所', value: 'S_10501' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【境内上市公司】-关联方境内上市公司', async () => {
    const companyId = '1731d3b93f80cca15d4273f7a1ea6bb2';
    const companyName = '陕西旅游发展股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyListed,
        fieldValue: [1],
        options: [{ label: '上市公司', value: 1 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.listedIndustry,
        fieldValue: ['S_10101', 'S_10401', 'S_10201', 'S_10301', 'S_10501'],
        options: [
          { label: '上交所主板', value: 'S_10101' },
          { label: '上交所科创板', value: 'S_10401' },
          { label: '深交所主板', value: 'S_10201' },
          { label: '深交所创业板', value: 'S_10301' },
          { label: '北交所', value: 'S_10501' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【上市进程】-关联方境内上市公司', async () => {
    const companyId = '1731d3b93f80cca15d4273f7a1ea6bb2';
    const companyName = '陕西旅游发展股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyListed,
        fieldValue: [1],
        options: [{ label: '上市公司', value: 1 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.listedIndustry,
        fieldValue: ['S_10101', 'S_10401', 'S_10201', 'S_10301', 'S_10501'],
        options: [
          { label: '上交所主板', value: 'S_10101' },
          { label: '上交所科创板', value: 'S_10401' },
          { label: '深交所主板', value: 'S_10201' },
          { label: '深交所创业板', value: 'S_10301' },
          { label: '北交所', value: 'S_10501' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【上市进程】-关联方拟上市', async () => {
    const companyId = '1731d3b93f80cca15d4273f7a1ea6bb2';
    const companyName = '陕西旅游发展股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.IPOProcess, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.applicationProgress,
        fieldValue: [1],
        options: [{ value: 1, label: '辅导备案' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hasTerminate,
        fieldValue: [2],
        options: [{ label: '否', value: 2 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail.length).toEqual(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(0);
  });

  it('【上市进程】-拟上市', async () => {
    const companyId = 'bd93a095107a3ce202132d7ff9cc1553';
    const companyName = '陕西旅游文化产业股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.IPOProcess, [
      {
        fieldKey: DimensionFieldKeyEnums.applicationProgress,
        fieldValue: [1],
        options: [{ value: 1, label: '辅导备案' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hasTerminate,
        fieldValue: [2],
        options: [{ label: '否', value: 2 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【对外投资企业涉金融】- 企业控股子公司-国标行业涉金融', async () => {
    const companyId = '11fd3c6aa524b90d61c77ec6d8cf2327';
    const companyName = '深圳赛博福纳资本投资管理中心（有限合伙）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.excludeCompanyName,
        fieldValue: ['有限合伙'],
        options: [{ value: '有限合伙', label: '有限合伙' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyIndustry,
        fieldValue: ['J'],
        accessScope: 2,
        options: [{ value: 'J', label: '金融业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【对外投资企业涉金融】- 企业控股子公司-企查查行业涉金融', async () => {
    const companyId = '11fd3c6aa524b90d61c77ec6d8cf2327';
    const companyName = '深圳赛博福纳资本投资管理中心（有限合伙）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.excludeCompanyName,
        fieldValue: ['有限合伙'],
        options: [{ value: '有限合伙', label: '有限合伙' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.qccIndustry,
        fieldValue: ['29'],
        accessScope: 2,
        options: [{ value: '29', label: '金融业' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('被列入失信被执行人', async () => {
    const companyId = '20c2e04be718ff4c3fe7847ac80f1ee3';
    const companyName = '宝能控股（中国）有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Self],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('近三年主体企业命中限制高消费', async () => {
    const companyId = '20c2e04be718ff4c3fe7847ac80f1ee3';
    const companyName = '宝能控股（中国）有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RestrictedConsumptionCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Self],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await creditEsService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditEsService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('近三年主体企业命中被执行人', async () => {
    const companyId = '';
    const companyName = '';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonExecution, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Self],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await creditEsService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditEsService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('近三年主体企业是否有刑事犯罪记录', async () => {
    const companyId = 'a99f766d973f4a8b4da8d0ebd6294e34';
    const companyName = '麻城市华祥再生资源循环利用有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Judgement, [
      // 案件类型 刑事案件
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        accessScope: 2,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      // 案件身份 被告
      {
        fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
        fieldValue: ['defendant'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近5年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await judgementService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('资产查冻', async () => {
    const companyId = '110ee63aac164b4c2b762f968ce48719';
    const companyName = '河南联通铝业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.AssetInvestigationAndFreezing, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.assetFreezeStatus,
        fieldValue: [1],
        accessScope: 2,
        options: [
          { label: '查封', value: 1 },
          { label: '解封', value: 2 },
        ],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await assertESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await assertESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('实控人命中失信被执行人', async () => {
    const companyId = '3f25ab4191af465b1d5d07f65f3e0667';
    const companyName = '北京盛世双木恒鑫置业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.ActualController],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('实控人命中限制高消费', async () => {
    const companyId = 'b94fc37ea3b40b276d842c922e0a845e';
    const companyName = '广西金永旺投资有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RestrictedConsumptionCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.ActualController],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditEsService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await creditEsService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('实控人命中被执行人', async () => {
    const companyId = '93cbf5ef4aa867781c8a0ce7d5789eb1';
    const companyName = '深圳市宝能投资集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonExecution, [
      /* {
                 fieldKey: DimensionFieldKeyEnums.cycle,
                 fieldValue: [1],
                 compareType: DimensionFieldCompareTypeEnums.GreaterThan,
               },*/
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.ActualController],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('近三年实控人是否有刑事犯罪记录', async () => {
    const companyId = '';
    const companyName = '';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Judgement, [
      // 排查对象 主要人员(在职)
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.ActualController],
        options: [
          { label: '董监高', value: TargetInvestigationEnums.MainStaff },
          { label: '法定代表人', value: TargetInvestigationEnums.Legal },
          { label: '实际控制人', value: TargetInvestigationEnums.ActualController },
          { label: '受益自然人', value: TargetInvestigationEnums.Benefit },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案件类型 刑事案件
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        accessScope: 2,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      // 案件身份 被告
      {
        fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
        fieldValue: ['defendant'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 不限
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await judgementService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('第一大股东命中失信被执行人', async () => {
    const companyId = 'f327437dc88da3296913f8f55ca6f6ea';
    const companyName = '深圳市钜盛华股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.LargestShareholder],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('第一大股东命中被执行人', async () => {
    const companyId = '93cbf5ef4aa867781c8a0ce7d5789eb1';
    const companyName = '深圳市宝能投资集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonExecution, [
      /* {
                 fieldKey: DimensionFieldKeyEnums.cycle,
                 fieldValue: [1],
                 compareType: DimensionFieldCompareTypeEnums.GreaterThan,
               },*/
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.LargestShareholder],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('第一大股东命中限制高消费', async () => {
    const companyId = 'f327437dc88da3296913f8f55ca6f6ea';
    const companyName = '深圳市钜盛华股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RestrictedConsumptionCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.LargestShareholder],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditEsService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await creditEsService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('近三年关联方企业企业是否有刑事犯罪记录', async () => {
    const companyId = '';
    const companyName = '';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Judgement, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        options: [
          { label: '董监高', value: TargetInvestigationEnums.MainStaff },
          { label: '法定代表人', value: TargetInvestigationEnums.Legal },
          { label: '实际控制人', value: TargetInvestigationEnums.ActualController },
          { label: '受益自然人', value: TargetInvestigationEnums.Benefit },
          { label: '关联方企业', value: TargetInvestigationEnums.IcbcSFRelated },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案件类型 刑事案件
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        accessScope: 2,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      // 案件身份 被告
      {
        fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
        fieldValue: ['defendant'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近5年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await judgementService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('【实际控制人的控制企业集中注册且无实缴资本】', async () => {
    //const id = 'c6ede3dce5b897825d6977738432d87d';
    const companyId = '8009a1ce20842af7a42d3589d7781ea7';
    const companyName = '华夏发展建设（北京）有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ControllerCompany, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.registerDate,
        fieldValue: [90],
        options: [{ unit: '天', min: 1, max: 365 }],
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.realRegistrationError,
        fieldValue: [1],
        options: RealRegistrationErrorMap,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【实控人对外投资企业涉房】-实控人控股子公司经营范围涉房', async () => {
    const companyId = '08d510c2ca7cdeee72ec147dd8fec1ec';
    const companyName = '河北中天房地产开发集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OutwardInvestment, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [RelatedTypeEnums.ActualController],
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companySocpe,
        fieldValue: ['土地开发', '地产开发', '商品房销售', '房地产项目投资'],
        accessScope: 2,
        options: [
          { value: '土地开发', label: '土地开发' },
          { value: '地产开发', label: '地产开发' },
          { value: '商品房销售', label: '商品房销售' },
          { value: '房地产项目投资', label: '房地产项目投资' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('近三年关联方命中失信被执行人', async () => {
    const companyId = '3f25ab4191af465b1d5d07f65f3e0667';
    const companyName = '北京盛世双木恒鑫置业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('近三年关联方命中限制高消费', async () => {
    const companyId = '0d67465dd5564f828f565eca7c6fa74e';
    const companyName = '广州宝投企业管理合伙企业（有限合伙）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RestrictedConsumptionCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await creditEsService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await creditEsService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('近三年关联方命中被执行人', async () => {
    const companyId = '93cbf5ef4aa867781c8a0ce7d5789eb1';
    const companyName = '深圳市宝能投资集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonExecution, [
      /* {
                 fieldKey: DimensionFieldKeyEnums.cycle,
                 fieldValue: [1],
                 compareType: DimensionFieldCompareTypeEnums.GreaterThan,
               },*/
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.IcbcSFRelated],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【红牌处罚】', async () => {
    const companyId = '97c7341d42dcfe45e1480b2a0a0ab23b';
    const companyName = '江苏久嘉工程建设有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.AdministrativePenalties, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishRedCard,
        fieldValue: [1],
        options: [
          { label: '非红牌处罚', value: 0 },
          { label: '是红牌处罚', value: 1 },
        ],
        accessScope: 0,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await supervisePunishService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await supervisePunishService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('【股权冻结】-主体企业', async () => {
    // 测试数据
    const companyId = '7c3945f19dd25049eaa25712a2698606';
    const companyName = '深圳龙懿新能源科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.FreezeEquity, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Self],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LianDate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await creditEsService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await creditEsService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  it('【股权质押】-主体企业', async () => {
    // 测试数据
    const companyId = 'd6fbd1c8f05a648cc854274045189139';
    const companyName = '上海域潇稀土股份有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.StockPledge, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeRole,
        fieldValue: [1, 2],
        options: [
          { label: '质押人参股企业', value: 1 },
          { label: '质押人', value: 2 },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseLibService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【股权质押】-第一大股东', async () => {
    // 测试数据
    const companyId = 'd6fbd1c8f05a648cc854274045189139';
    const companyName = '上海域潇稀土股份有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.StockPledge, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.LargestShareholder],
        options: PersonCaseTargetInvestigation,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeRole,
        fieldValue: [1, 2],
        options: [
          { label: '质押人参股企业', value: 1 },
          { label: '质押人', value: 2 },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseLibService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【股东列表是否包含投资机构】-股东列表是否包含投资机构', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ShareholderInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
        fieldValue: [1],
        options: IsInstitutionalInvestorConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【股东列表是否包含投资机构】-股东列表不包含投资机构', async () => {
    const companyId = 'f51294e83da764648ec587b60328a370';
    const companyName = '上海逐风信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ShareholderInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
        fieldValue: [2],
        options: IsInstitutionalInvestorConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【实控人近三年有无减持公司股权】- 实控人近三年有减持公司股权，排除VC、PE等节点', async () => {
    const companyId = '1e5ae70583905068b60d6c7b07bc6d1c';
    const companyName = '上海畅德医疗科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [1],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [72],
        options: [{ value: 72, label: '成员变更' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [2],
        options: HolderRoleType,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isPEVC,
        fieldValue: [0],
        options: IsPEVCMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    const detail = await riskChangeEsSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await riskChangeEsSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
  it('【近三年主体企业是否有刑事犯罪记录（排除交通肇事罪）】-立案信息', async () => {
    const companyId = '79ed69f2e0331f058a031d09863c1cbb';
    const companyName = '中国平安财产保险股份有限公司乌鲁木齐中心支公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.JudicialCase, [
      // 案件类型 刑事案件
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        accessScope: 2,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      // 案件身份 被告
      {
        fieldKey: DimensionFieldKeyEnums.JudicialCaseRoleType,
        fieldValue: ['D'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseTag,
        fieldValue: [6],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
});
