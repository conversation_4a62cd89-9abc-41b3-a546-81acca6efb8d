import { Injectable } from '@nestjs/common';
import { v1 as uuidv1 } from 'uuid';
import { PaginationParams, PaginationResponse } from 'libs/model/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DiligenceHistoryService } from '../details/diligence.history.service';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import * as moment from 'moment';
import * as Bluebird from 'bluebird';
import { QueueService } from '../../../libs/config/queue.service';
import { DiligenceSnapshotEsService } from './diligence.snapshot.es.service';
import { ApiResponseStatusEnum } from '../../../libs/enums/ApiResponseStatusEnum';
import { OperationEnums } from '../../../libs/enums/diligence/OperationEnums';
import { convertSnapshotDimensionFieldForSort } from './utils.snapshot';
import { find, intersection } from 'lodash';
import { SnapshotDimensionMessagePO, SnapshotMessageBasePO } from '../../../libs/model/diligence/snapshot/SnapshotDimensionMessagePO';
import { GetSnapshotHitDetailsParamPO } from './po/GetSnapshotHitDetailsParamPO';
import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchDiligenceEntity } from '../../../libs/entities/BatchDiligenceEntity';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
// import { DiligenceSnapshotService } from './diligence.snapshot.service';
// import { BaseDimensions } from '../../../libs/constants/dimension.constants';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { SnapshotStatus } from '../../../libs/model/diligence/SnapshotDetail';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { flattenDimensionHitResultPO } from '../../../libs/utils/diligence/dimension.utils';
import { SnapshotSearchDimensionContentResponse } from './po/SnapshotSearchPaginationResponse';

@Injectable()
export class DiligenceSnapshotHelper {
  public snapshotQueue: RabbitMQ;
  private logger: Logger = QccLogger.getLogger(DiligenceSnapshotHelper.name);

  constructor(
    // private readonly diligenceSnapshotService: DiligenceSnapshotService,
    private readonly diligenceHistoryService: DiligenceHistoryService,
    private readonly queueService: QueueService,
    private readonly snapshotEsService: DiligenceSnapshotEsService,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    @InjectRepository(DiligenceHistoryEntity) protected readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
  ) {
    this.snapshotQueue = this.queueService.snapshotQueue;
  }

  // NOTE: 使用 snapshotId + dimensionKey 作为 cacheKey

  public async getSnapshotHitDetails(params: GetSnapshotHitDetailsParamPO): Promise<HitDetailsBaseResponse> {
    const { snapshotId, dimensionKey, sortField, fetchAll, companyName, sortOrder, esFilter, strategyId } = params;
    let pagination = params.pagination;
    if (!pagination) {
      pagination = {
        pageSize: 10,
        pageIndex: 1,
      };
    }
    let res: HitDetailsBaseResponse = {
      Result: [],
      Paging: {
        PageSize: pagination.pageSize,
        PageIndex: pagination.pageIndex,
        TotalRecords: 0,
      },
      status: ApiResponseStatusEnum.OK,
      GroupItems: [],
    };
    const dbDiligence = await this.diligenceHistoryService.getDiligenceBySnapshotId(snapshotId);
    const hits = dbDiligence?.details?.dimensionHits || [];
    if (!dbDiligence || intersection(hits, dimensionKey).length !== dimensionKey.length) {
      // 没找到 尽调记录或者尽调记录中没命中指定的维度， 直接返回null，交给上次调用者处理
      this.logger.warn(`快照里面没有值, snapshotId=${snapshotId}, dimensionKey=${dimensionKey}`);
      return res;
    }
    const dimensionHit = intersection(hits, dimensionKey).length == dimensionKey.length; // 维度被命中了
    if (!dimensionHit) {
      // 维度原始就没有命中，直接返回空数据
      return res;
    }

    const snapshotAllFinished = dbDiligence.snapshotDetails?.status == 1;
    const dimensionAlreadyHit = intersection(hits, dimensionKey).length == dimensionKey.length;
    // 显示命中了并且指定维度的快照也被标记完成了
    const dimensionUniqueKey = dimensionKey[0] + '_' + strategyId;
    const snapshotShouldFinished = intersection(dbDiligence.snapshotDetails?.successHits || [], [dimensionUniqueKey]).length == 1;
    const inRightTimeRange = moment().diff(moment(dbDiligence.createDate), 'minutes') > 15; //尽调已经创建了超过15
    if (!snapshotAllFinished && inRightTimeRange) {
      //如果超过指定的时间快照还未被标记完成，需要检查是否是状态更新出了问题
      this.logger.warn(`snapshot status is not updated correctly,diligenceId=${dbDiligence.id}, snapshotId=${snapshotId}, will refresh now.`);
      const esFinishedDimensionKeys = await this.snapshotEsService.findSnapshotedDimensionByDiligenceId(dbDiligence.id);
      if (intersection(esFinishedDimensionKeys, hits).length === hits.length) {
        this.logger.info(`snapshot already finished, will update snapshot status now, diligenceId=${dbDiligence.id}`);
        await this.diligenceHistoryRepo.update(dbDiligence.id, {
          snapshotDetails: {
            status: SnapshotStatus.SUCCESS,
            successHits: hits,
          },
        });
      }
    }

    if (!snapshotShouldFinished) {
      if (dimensionAlreadyHit) {
        //维度 已经命中
        if (snapshotAllFinished || inRightTimeRange) {
          //如果 快照整体已经标记完成或者 尽调时间已经过去15分钟
          res.status = ApiResponseStatusEnum.FAILED;
          await this.reFillSnapshotData(dbDiligence, dimensionKey);
        } else {
          res.status = ApiResponseStatusEnum.Processing;
        }
      } else {
        //维度 未命中
        res.status = ApiResponseStatusEnum.OK;
      }
      return res;
    }

    // 维度的快照状态已经完成， 开始读取快照数据
    const { orgId, companyId, id: diligenceId } = dbDiligence;
    const resultItems = [];
    let total = 0;
    if (sortField || fetchAll) {
      //params.esFilter?.filter 有值的话说明是要通过es 直接进行过滤以及后续的排序等
      // 1. 如果还是用的，说明是要使用es直接过滤排序等
      const pageSize = 1000;
      let pageIndex = 1;
      let aggs: any;
      do {
        const searchRes: PaginationResponse = await this.snapshotEsService.searchSnapshotData(
          {
            orgId,
            diligenceId: [diligenceId],
            companyId,
            dimensionKey,
            pageSize,
            pageIndex,
            esFilter: params.esFilter,
            strategyId,
          },
          true,
        );
        total += searchRes.data.length;
        if (searchRes.data) {
          resultItems.push(...searchRes.data);
        }
        aggs = searchRes.aggs;
        if (!searchRes.data || total === searchRes.total || searchRes.data.length < pageSize) {
          break;
        }
        pageIndex++;
      } while (true);
      resultItems.sort((a, b) => {
        const valueA = convertSnapshotDimensionFieldForSort(a[sortField]);
        const valueB = convertSnapshotDimensionFieldForSort(b[sortField]);
        return sortOrder?.toUpperCase() === 'ASC' ? (valueA > valueB ? 1 : -1) : valueA < valueB ? 1 : -1;
      });
      res = this.readPaginationData(resultItems, pagination);
      res.aggs = aggs;
    } else {
      const searchRes: PaginationResponse = await this.snapshotEsService.searchSnapshotData(
        {
          companyId,
          orgId,
          diligenceId: [diligenceId],
          dimensionKey: dimensionKey,
          pageSize: pagination.pageSize,
          pageIndex: pagination.pageIndex,
          esFilter: params.esFilter,
          strategyId,
        },
        true,
      );
      total = searchRes.total;
      resultItems.push(...searchRes.data);
      res.Result = resultItems;
      res.Paging.TotalRecords = total;
      res.aggs = searchRes.aggs;
    }
    if (res.Result.length === 0) {
      if (params.esFilter?.filter) {
        // 说明是通过条件过滤过的，可能就是没有符合条件的数据，直接返回空数据
        return res;
      }
      if (inRightTimeRange) {
        // 维度快照被标记完成，但是还没有获取到数据
        res.status = ApiResponseStatusEnum.FAILED;
        await this.reFillSnapshotData(dbDiligence, dimensionKey, true);
      }
      res.status = ApiResponseStatusEnum.Processing;
    } else if (dimensionKey.includes(DimensionTypeEnums.NoCapital) || dimensionKey.includes(DimensionTypeEnums.BusinessAbnormal4)) {
      // RA-6997 久无实缴、被列入非正常户 返回值改为二位数组
      if (res.Result.length > 1) {
        res.Result = [res.Result];
      }
    }

    return res;
  }

  @TraceLog({ throwError: true })
  public async reFillSnapshotData(dbDiligence: DiligenceHistoryEntity, dimensionKeys: DimensionTypeEnums[], atOnce = false) {
    const { id: diligenceId, name: companyName, snapshotId } = dbDiligence;
    const allDefs = flattenDimensionHitResultPO(dbDiligence.details.originalHits);

    await Bluebird.map(dimensionKeys, async (dimensionKey) => {
      const scorePO = find(allDefs, { dimensionKey });
      this.logger.warn(`refill snapshot data,diligenceId=${diligenceId}, dimensionKey=${dimensionKey} `);
      if (scorePO) {
        const { orgId, operator: userId, companyId } = dbDiligence;
        const m: SnapshotDimensionMessagePO = {
          orgId,
          userId,
          diligenceId,
          operation: OperationEnums.DimensionSnapshot,
          scorePO,
          companyId,
          companyName,
          snapshotId,
          diligenceAt: dbDiligence.createDate,
          batchIds: dbDiligence.batchEntities?.map((t) => t.batchId),
          refill: 1,
        };
        if (atOnce) {
          // 立即重新生成快照数据
          // await this.diligenceSnapshotService.processSnapshotMessage(m, SnapshotQueueTypeEnums.Diligence);
        } else {
          await this.snapshotQueue.sendMessageV2(m, { retries: 3 });
        }
      } else {
        this.logger.warn(`refill snapshot data failed, no scorePO found for diligenceId=${diligenceId}, dimensionKey=${dimensionKey}`);
      }
    });
  }

  /**
   * @Depracted 获取尽调快照指定维度的详情数据， 导出pdf使用
   * @param snapshotId
   * @param matchType
   */
  public async getDiligenceDetails(dbDiligence: DiligenceHistoryEntity, dimensionHitStrategy: DimensionHitStrategyPO[]) {
    const resultObj = {};
    const { snapshotId, id } = dbDiligence;
    if (dimensionHitStrategy.length) {
      await Bluebird.map(dimensionHitStrategy, async (dimension: DimensionHitStrategyPO) => {
        const details = Object.assign(new SnapshotSearchDimensionContentResponse(), {
          pageIndex: 1,
          pageSize: 99,
          displayKey: dimension?.extendJson?.displayKey,
        });
        const resultItems = [];
        let total = 0;
        const pageSize = 1000;
        let pageIndex = 1;
        let aggs: any;
        do {
          const searchRes: PaginationResponse = await this.snapshotEsService.searchSnapshotData(
            {
              snapshotId: [snapshotId],
              dimensionKey: [dimension.dimensionDef.key],
              strategyId: dimension.strategyId,
              diligenceId: [id],
              pageSize,
              pageIndex,
            },
            true,
          );
          total += searchRes.data.length;
          if (searchRes.data) {
            resultItems.push(...searchRes.data);
          }
          aggs = searchRes.aggs;
          if (!searchRes.data || total === searchRes.total || searchRes.data.length < pageSize) {
            break;
          }
          pageIndex++;
        } while (true);
        // const details = await this.readOssFile(snapshotId, key);
        const dimensionSortField = dimension?.getSortField();
        const sortField = dimensionSortField?.fieldSnapshot || dimensionSortField?.field;
        const sortOrder = dimensionSortField?.order || 'DESC';
        //改为直接从es中获取数据, 暂时还是只获取前99条数据
        // const params: SearchSnapshotPO = {
        //   snapshotId: [snapshotId],
        //   dimensionKey: [dimension.dimensionDef.key],
        //   strategyId: dimension.strategyId,
        //   diligenceId: [id],
        //   pageSize: 99,
        //   pageIndex: 1,
        //   sort: {
        //     field: sortField,
        //     order: sortOrder,
        //   },
        // };
        //const details = await this.snapshotEsService.searchSnapshotData(params, true);
        details.total = resultItems.length;
        details.aggs = aggs;
        if (resultItems.length) {
          resultItems.forEach((item) => {
            item['dimensionKey'] = dimension.key;
            item['strategyId'] = dimension.strategyId;
          });
          if (dimensionSortField && resultItems?.length && sortField) {
            resultItems.sort((a, b) => {
              const valueA = convertSnapshotDimensionFieldForSort(a[sortField]);
              const valueB = convertSnapshotDimensionFieldForSort(b[sortField]);
              return sortOrder?.toUpperCase() === 'ASC' ? (valueA > valueB ? 1 : -1) : valueA < valueB ? 1 : -1;
            });
          }
          details.data = resultItems.slice(0, 99);
        }
        if (details?.data?.length) {
          resultObj[`${dimension.key}-${dimension.strategyId}`] = details;
        }
      });
    }
    return resultObj;
  }

  public async generateSnapshotId() {
    return uuidv1();
  }

  // /**
  //  *
  //  * 目前临时给 updateSnapshot 的时候用， 后续可以优化，
  //  * 直接使用 DiligenceSnapshotEsService 的 changeSnapshotDataStatus 方法
  //  *
  //  * @param snapshotId
  //  * @param dimensionKey
  //  * @param data
  //  */
  // public async saveDimensionData(snapshotId: string, dimensionKey: DimensionTypeEnums | string, data: object, override = false) {
  //   const diligenceEntity = await this.diligenceHistoryService.getDiligenceBySnapshotId(snapshotId);
  //   if (!diligenceEntity) {
  //     throw new Error(`未找到对应的尽调记录:${snapshotId}`);
  //   }
  //   const { id: diligenceId, createDate: diligenceAt, companyId, orgId } = diligenceEntity;
  //   if (override) {
  //     //如果是覆盖，先删除原有数据，该逻辑主要用来兼容 updatePersonRelatedSnapshot 方法， 后续可以优化使用统一的 .modifyService.saveExcludeIds 方法处理
  //     await this.snapshotEsService.removeSnapshotDiligenceData({
  //       orgId,
  //       diligenceId,
  //       dimensionKeys: [dimensionKey as DimensionTypeEnums],
  //     });
  //   }
  //   await this.snapshotEsService.insertSnapshotData(
  //     {
  //       snapshotId,
  //       dimensionKey,
  //       orgId,
  //       diligenceId,
  //       diligenceAt,
  //       companyId,
  //       batchId: diligenceEntity?.batchEntities?.map((t) => t.batchId),
  //       items: data as any[],
  //     },
  //     true,
  //   );
  //
  //   // const filename = `${dimensionKey}.json`;
  //   // const filepath = `${tmpdir()}/${snapshotId}_${filename}`;
  //   // writeFileSync(filepath, JSON.stringify(data), { encoding: 'utf-8' });
  //   // const folder = await this.getOssFolder(snapshotId);
  //   // const ossObject = this.configService.getOssObject(`${folder}`, filename);
  //   // this.logger.debug(`save snapshot data to oss file: ${ossObject}`);
  //   // await this.ossService.putStream(ossObject, createReadStream(filepath));
  // }

  private readPaginationData(items: any[], pagination: PaginationParams): HitDetailsBaseResponse {
    const res: HitDetailsBaseResponse = new HitDetailsBaseResponse();
    const { pageSize, pageIndex } = pagination;
    res.Paging = {
      PageSize: pageSize,
      PageIndex: pageIndex,
      TotalRecords: 0,
    };
    if (!items) {
      res.Result = null;
      return res;
    }
    if (items.length === 0) {
      res.Result = [];
      return res;
    }
    res.Paging.TotalRecords = items.length;
    const start = (pageIndex - 1) * pageSize;
    if (start >= items.length) {
      res.Result = [];
    } else {
      res.Result = items.slice(start, start + pageSize);
    }
    return res;
  }

  public async refreshBatchSnapshotRelation(batchId: number, orgId: number, diligenceIds?: number[]) {
    if (diligenceIds?.length) {
      await this.snapshotEsService.refreshBatchDiligenceRelation(diligenceIds, batchId);
    } else {
      //针对批量排查(包括年检), 找出指定batch 的 batch result，根据resultType 找出执行了尽调但是未付费的 SUCCEED_UNPAID
      let pageIndex = 1;
      const pageSize = 400;
      // const qb = this.batchResultRepo.createQueryBuilder('batchResult');
      // qb.where('batchResult.batchId = :batchId ', { batchId })
      //   .andWhere('batchResult.resultType = :resultType', { resultType: BatchJobResultTypeEnums.SUCCEED_UNPAID })
      //   .select(['resultId', 'batchId', 'info', 'resultType']);
      const qb = this.batchDiligenceRepo.createQueryBuilder('batchDiligence');
      qb.where('batchDiligence.batchId = :batchId ', { batchId }).select(['batchDiligence.id', 'batchDiligence.batchId', 'batchDiligence.diligenceId']);
      do {
        qb.skip((pageIndex - 1) * pageSize).take(pageSize);
        const items = await qb.getMany();
        const diligenceIds = items.map((i) => i.diligenceId);
        if (diligenceIds?.length > 0) {
          this.logger.info(`refresh batch snapshot relation, batchId=${batchId}, diligenceSize=${diligenceIds.length}`);
          await this.snapshotEsService.refreshBatchDiligenceRelation(diligenceIds, batchId);
        }

        if (items.length == 0 || items.length < pageSize) {
          break;
        }
        pageIndex++;
      } while (true);
    }
  }

  public async createSnapshot(snapshotMsg: SnapshotDimensionMessagePO) {
    await this.snapshotQueue.sendMessageV2(snapshotMsg, {
      retries: 3,
      breakTrace: true,
    });
  }

  public async createSnapshotByBatch(batchId: number, orgId: number) {
    // @ts-ignore
    const snapshotMsg: SnapshotMessageBasePO = {
      orgId,
      operation: OperationEnums.ReCreateByBatch,
      batchIds: [batchId],
    };
    await this.snapshotQueue.sendMessageV2(snapshotMsg, {
      retries: 3,
      breakTrace: true,
    });
  }
}
