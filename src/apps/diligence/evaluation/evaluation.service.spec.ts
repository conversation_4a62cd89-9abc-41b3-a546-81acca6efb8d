import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RiskModelInitService } from 'apps/risk_model/init_mode/risk_model.init.service';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
import * as Bluebird from 'bluebird';
import * as _ from 'lodash';
import { EntityManager, getConnection } from 'typeorm';
import { DescriptionRegex, ForbiddenStandardCode } from '../../../libs/constants/common';
import { BatchDiligenceEntity } from '../../../libs/entities/BatchDiligenceEntity';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { BatchResultEntity } from '../../../libs/entities/BatchResultEntity';
import { DiligenceExcludesEntity } from '../../../libs/entities/DiligenceExcludesEntity';
import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
import { DiligenceRemarkEntity } from '../../../libs/entities/DiligenceRemarkEntity';
import { DistributedSystemResourceEntity } from '../../../libs/entities/DistributedSystemResourceEntity';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { DataStatusEnums, InactiveDataStatus } from '../../../libs/enums/DataStatusEnums';
import { DistributedResourceTypeEnums } from '../../../libs/enums/DistributedResourceTypeEnums';
import { ProductCodeEnums } from '../../../libs/enums/ProductCodeEnums';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import { PlatformUser } from '../../../libs/model/common';
import { DimensionAllHitPO } from '../../../libs/model/diligence/dimension/DimensionAllHitPO';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { ModelScorePO } from '../../../libs/model/diligence/ModelScorePO';
import { DiligenceResponse } from '../../../libs/model/diligence/req&res/DiligenceResponseV2';
import { GetDiligenceResultRequest } from '../../../libs/model/diligence/req&res/GetDiligenceResultRequest';
import { SingleCompanyDiligenceRequest } from '../../../libs/model/diligence/req&res/SingleCompanyDiligenceRequest';
import { AppTestModule } from '../../app/app.test.module';
import { CompanySearchService } from '../../company/company-search.service';
import { getAllDimensionHitStrategy } from '../../dimension/dimension.helper';
import { RiskModelModule } from '../../risk_model/risk_model.module';
import { RiskModelService } from '../../risk_model/risk_model.service';
import { GetDimensionHitResult, MockCompanyInfoConstant } from '../../test_utils_module/diligence.test.utils';
import { clearAllTestRiskModelTestData } from '../../test_utils_module/riskmodel.test.utils';
import { DiligenceModifyService } from '../details/diligence.modify.service';
import { DiligenceModule } from '../diligence.module';
import { DiligenceSnapshotService } from '../snapshot/diligence.snapshot.service';
import { SnapshotQueueTypeEnums } from '../snapshot/po/SnapshotQueueTypeEnums';
import { EvaluationService } from './evaluation.service';
import { HitDimensionService } from './hit.dimension.service';
import { RiskScoreService } from './risk.score.service';

jest.setTimeout(600000);
describe('EvaluationService Test', () => {
  process.env.MOCK_MESSAGE_QUEUE_NOT = 'true';
  process.env.STAGE = 'local';
  let testRiskModel: RiskModelEntity;
  let entityManager: EntityManager;

  let evaluationService: EvaluationService;
  let companySearchService: CompanySearchService;
  let riskModelInitService: RiskModelInitService;
  let riskModelService: RiskModelService;
  let diligenceSnapshotService: DiligenceSnapshotService;
  let hitDimensionService: HitDimensionService;

  // 生成测试用户ID
  const [testOrgId, testUserId] = generateUniqueTestIds('EvaluationService.service.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        DiligenceModule,
        AppTestModule,
        RiskModelModule,
        TypeOrmModule.forFeature([
          DiligenceHistoryEntity,
          DiligenceRemarkEntity,
          DiligenceExcludesEntity,
          BatchEntity,
          BatchResultEntity,
          BatchDiligenceEntity,
          DistributedSystemResourceEntity,
          RiskModelEntity,
        ]),
      ],
      providers: [
        EvaluationService,
        RiskScoreService,
        {
          provide: DiligenceSnapshotService,
          useValue: {
            prepareSnapshot: jest.fn(),
          },
        },
        {
          provide: HitDimensionService,
          useValue: {
            getDimensionHitResult: jest.fn(),
          },
        },
        {
          provide: DiligenceModifyService,
          useValue: jest.fn(),
        },
        {
          provide: CompanySearchService,
          useValue: {
            companyDetailsQcc: jest.fn(),
          },
        },
      ],
    }).compile();

    entityManager = module.get<EntityManager>(EntityManager);
    evaluationService = module.get<EvaluationService>(EvaluationService);
    companySearchService = module.get<CompanySearchService>(CompanySearchService);
    riskModelInitService = module.get<RiskModelInitService>(RiskModelInitService);
    riskModelService = module.get<RiskModelService>(RiskModelService);
    diligenceSnapshotService = module.get<DiligenceSnapshotService>(DiligenceSnapshotService);
    hitDimensionService = module.get<HitDimensionService>(HitDimensionService);

    // 创建测试风险模型
    const modelName = `【集成测试】尽调模型-${Date.now()}`;
    testRiskModel = await riskModelInitService.createHTFModel(testUser, modelName, testUser.currentOrg, RiskModelTypeEnums.GeneralRiskModel);
  });

  beforeEach(async () => {
    jest.clearAllMocks();
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // 清理测试数据
    await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel, [testRiskModel.modelId]);
    jest.restoreAllMocks();
    const connection = getConnection();
    await connection.close();
  });

  describe('getPaidCheckList 方法集成测试', () => {
    let defaultRiskModelEntity: RiskModelEntity;
    beforeEach(async () => {
      // 创建测试风险模型
      const modelName = `【集成测试】尽调模型-${Date.now()}`;
      defaultRiskModelEntity = await riskModelInitService.createHTFModel(testUser, modelName, testUser.currentOrg, RiskModelTypeEnums.GeneralRiskModel);
      // 如果需要特定状态，更新模型状态
      await entityManager.update(RiskModelEntity, defaultRiskModelEntity.modelId, { status: DataStatusEnums.Enabled });
    });

    afterEach(async () => {
      if (defaultRiskModelEntity?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel, [defaultRiskModelEntity.modelId]);
      }
      await entityManager.delete(DiligenceHistoryEntity, { orgId: testOrgId });
      await entityManager.delete(BatchEntity, { orgId: testOrgId });
    });

    it('计费场景：验证不同产品的计费逻辑', async () => {
      // Given
      const companyId = 'test-key-no';
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId];
      // 创建历史尽调记录，用于测试是否首次尽调
      await entityManager.save(DiligenceHistoryEntity, {
        orgId: testOrgId,
        companyId,
        name: '测试公司',
        operator: testUserId,
        orgModelId: defaultRiskModelEntity.modelId,
        modelBranchCode: defaultRiskModelEntity.branchCode,
        product: ProductCodeEnums.Pro,
        paid: 1,
        details: {},
        shouldUpdate: 0,
      });
      // 设置用户产品为 Pro
      const proUser = { ...testUser, currentProduct: ProductCodeEnums.Pro };
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockReturnValue(Promise.resolve(MockCompanyInfoConstant as any));
      // When
      const proResult = await evaluationService.getPaidCheckList(proUser, request);
      // Then - Pro 产品应该按模型级别计费
      expect(proResult.list[0].paid).toBe(0); // 已经有记录，所以不应该计费
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalled();
    });

    it('基础场景：成功获取付费核查列表', async () => {
      // Given
      const companyId = 'test-key-no';
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId];
      // When
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockReturnValue(Promise.resolve(MockCompanyInfoConstant as any));
      const result = await evaluationService.getPaidCheckList(testUser, request);
      // Then
      expect(result).toBeDefined();
      expect(result.list).toHaveLength(1);
      expect(result.list[0]).toMatchObject({
        companyId,
        orgModelId: defaultRiskModelEntity.modelId,
        riskModelName: defaultRiskModelEntity.modelName,
        paid: expect.any(Number), // 根据实际情况可能是0或1
      });
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalled();
    });

    it('异常场景：企业类型不支持排查', async () => {
      // Given
      const companyId = 'test-key-no';
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId];
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockReturnValue(
        Promise.resolve({
          ...MockCompanyInfoConstant,
          standardCode: ForbiddenStandardCode, // 使用实际的禁止企业类型常量
        } as any),
      );
      // When & Then
      await expect(evaluationService.getPaidCheckList(testUser, request)).rejects.toThrow();
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalled();
    });

    it('异常场景：企查查接口返回空数据', async () => {
      // Given
      const companyId = 'test-key-no';
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId];
      // 设置企查查返回空数据
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockReturnValue(Promise.resolve(null));
      // When & Then
      await expect(evaluationService.getPaidCheckList(testUser, request)).rejects.toThrow();
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalled();
    });

    it('异常场景：企查查接口调用失败', async () => {
      // Given
      const companyId = 'test-key-no';
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId];
      // 设置企查查返回空数据
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockRejectedValue(new Error('API调用失败'));
      // When & Then
      await expect(evaluationService.getPaidCheckList(testUser, request)).rejects.toThrow('API调用失败');
    });

    it('多模型场景：获取多个模型的付费核查列表-一个企业支持多个模型核查计费', async () => {
      // Given
      const companyId = 'test-key-no';

      // 创建第二个测试风险模型
      const secondRiskModelEntity = await riskModelInitService.createHTFModel(
        testUser,
        `【集成测试】尽调模型-${Date.now()}`,
        testUser.currentOrg,
        RiskModelTypeEnums.GeneralRiskModel,
      );

      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId, secondRiskModelEntity.modelId];
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockReturnValue(Promise.resolve(MockCompanyInfoConstant as any));
      // When
      const result = await evaluationService.getPaidCheckList(testUser, request);

      // Then
      expect(result).toBeDefined();
      expect(result.list).toHaveLength(2);

      // 验证第一个模型
      expect(result.list[0]).toMatchObject({
        companyId,
        orgModelId: defaultRiskModelEntity.modelId,
        riskModelName: defaultRiskModelEntity.modelName,
      });

      // 验证第二个模型
      expect(result.list[1]).toMatchObject({
        companyId,
        orgModelId: secondRiskModelEntity.modelId,
        riskModelName: secondRiskModelEntity.modelName,
      });

      // 验证企查查接口只被调用了一次
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalledTimes(1);
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalledWith(companyId);
    });
  });

  describe('getRiskList 方法测试', () => {
    let defaultRiskModelEntity: RiskModelEntity;
    // 2. 准备模拟的企业信息数据
    const mockCompanyInfo: any = MockCompanyInfoConstant;
    let mockDimensionHitResult: DimensionAllHitPO;
    beforeEach(async () => {
      // 创建测试风险模型
      const modelName = `【集成测试】尽调模型-${Date.now()}`;
      const riskModel = await riskModelInitService.createHTFModel(testUser, modelName, testUser.currentOrg, RiskModelTypeEnums.GeneralRiskModel);
      defaultRiskModelEntity = await riskModelService.getRiskModelFullDetails(riskModel.modelId, testUser);
      const dimHitStrategies: DimensionHitStrategyPO[] = getAllDimensionHitStrategy(defaultRiskModelEntity, InactiveDataStatus);
      // 2，初始化 DimensionHitResult  并替换 strategyId
      mockDimensionHitResult = JSON.parse(JSON.stringify(GetDimensionHitResult)) as DimensionAllHitPO;
      if (dimHitStrategies?.length) {
        mockDimensionHitResult.allHits = mockDimensionHitResult.allHits.map((hit) => {
          const matchedStrategy = dimHitStrategies.find((strategy) => strategy.strategyName === hit.strategyName);
          if (matchedStrategy) {
            return {
              ...hit,
              strategyId: matchedStrategy.strategyId,
            };
          }
          return hit;
        });
      }
    });

    afterEach(async () => {
      if (defaultRiskModelEntity?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel, [defaultRiskModelEntity.modelId]);
      }
      await entityManager.delete(DiligenceHistoryEntity, { orgId: testOrgId });
      await entityManager.delete(BatchEntity, { orgId: testOrgId });
    });

    // 假设hitDimensionService.getDimensionHitResult已经正确返回查询的结果
    it('基础场景：成功获取单个模型的风险评估结果', async () => {
      const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId];
      request.cacheHours = 0; // 不使用缓存，强制重新评估

      // 设置 spy 的默认返回值
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockResolvedValue(mockCompanyInfo);

      // 模拟 hitDimensionService 的 getDimensionHitResult 方法 - 增加timeCost属性
      mockDimensionHitResult.timeCost = { total: 100 } as any; // 确保DimensionAllHitPO类型完整
      jest.spyOn(hitDimensionService, 'getDimensionHitResult').mockResolvedValue(mockDimensionHitResult);

      // 模拟 snapshotService 的 prepareSnapshot 方法
      jest.spyOn(diligenceSnapshotService, 'prepareSnapshot').mockResolvedValue('test-snapshot-id');

      const results = await evaluationService.getRiskList(testUser, request);

      expect(results).toHaveLength(1);
      const result = results[0] as DiligenceResponse;

      // 验证基本信息
      expect(result).toMatchObject({
        companyId,
        name: mockCompanyInfo.Name,
        orgModelId: defaultRiskModelEntity.modelId,
        modelBranchCode: defaultRiskModelEntity.branchCode,
        cached: false,
        riskModelName: defaultRiskModelEntity.modelName,
      });

      // 验证工商接口被调用
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalled();

      // 验证获取维度数据被调用
      expect(hitDimensionService.getDimensionHitResult).toHaveBeenCalledWith(
        testUser.currentOrg,
        companyId,
        mockCompanyInfo.Name,
        expect.objectContaining({ modelId: defaultRiskModelEntity.modelId }),
        undefined,
      );

      // 验证快照服务被调用
      expect(diligenceSnapshotService.prepareSnapshot).toHaveBeenCalled();

      // 验证维度命中结果的具体内容
      const modelScorePO: ModelScorePO = result.details;
      expect(modelScorePO).toBeDefined();
      expect(modelScorePO.dimensionHits).toBeDefined();

      // 确保维度命中结果正确且唯一
      const expectedDimensionKeys = _.uniq(mockDimensionHitResult.allHits.map((t) => t.dimensionKey));
      expect(modelScorePO.dimensionHits).toEqual(expect.arrayContaining(expectedDimensionKeys));

      // 验证费用信息
      expect(modelScorePO.cost).toBeDefined();

      // 验证数据库中的尽调记录
      const dbRecords = await entityManager.find(DiligenceHistoryEntity, {
        where: { companyId, orgModelId: defaultRiskModelEntity.modelId },
      });
      expect(dbRecords.length).toBeGreaterThan(0);
      expect(dbRecords[0]).toMatchObject({
        companyId,
        orgModelId: defaultRiskModelEntity.modelId,
        name: mockCompanyInfo.Name,
      });
    });

    it('多模型场景：成功获取多个模型的风险评估结果', async () => {
      // Given
      const companyId = 'f625a5b661058ba5082ca508f99ffe1b';

      // 创建第二个测试风险模型
      const secondModelName = `【集成测试】尽调模型-${Date.now()}`;
      const secondRiskModelEntity = await riskModelInitService.createHTFModel(
        testUser,
        secondModelName,
        testUser.currentOrg,
        RiskModelTypeEnums.GeneralRiskModel,
      );
      // 更新模型状态为已启用
      await entityManager.update(RiskModelEntity, secondRiskModelEntity.modelId, { status: DataStatusEnums.Enabled });

      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId, secondRiskModelEntity.modelId];
      request.cacheHours = 0; // 不使用缓存，强制重新评估

      // When
      // 设置 spy 的默认返回值
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockResolvedValue(mockCompanyInfo);
      // 模拟 hitDimensionService 的 getDimensionHitResult 方法
      jest.spyOn(hitDimensionService, 'getDimensionHitResult').mockResolvedValue(mockDimensionHitResult);
      // 模拟 snapshotService 的 prepareSnapshot 方法
      jest.spyOn(diligenceSnapshotService, 'prepareSnapshot').mockResolvedValue(undefined);
      const results = await evaluationService.getRiskList(testUser, request);

      // Then
      expect(results).toHaveLength(2);

      // 验证第一个模型的结果
      const result1 = results[0] as DiligenceResponse;
      expect(result1).toMatchObject({
        companyId,
        name: mockCompanyInfo.Name,
        orgModelId: defaultRiskModelEntity.modelId,
        modelBranchCode: defaultRiskModelEntity.branchCode,
        riskModelName: defaultRiskModelEntity.modelName,
      });

      // 验证第二个模型的结果
      const result2 = results[1] as DiligenceResponse;
      expect(result2).toMatchObject({
        companyId,
        name: mockCompanyInfo.Name,
        orgModelId: secondRiskModelEntity.modelId,
        modelBranchCode: secondRiskModelEntity.branchCode,
        riskModelName: secondRiskModelEntity.modelName,
      });

      // 验证企查查接口只被调用了一次
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalledTimes(1);

      // 验证维度命中结果服务被调用
      expect(hitDimensionService.getDimensionHitResult).toHaveBeenCalledWith(
        testUser.currentOrg,
        companyId,
        mockCompanyInfo.Name,
        expect.objectContaining({ modelId: defaultRiskModelEntity.modelId }),
        undefined,
      );

      // 验证批次创建
      const batchEntities = await entityManager.find(BatchEntity, {
        where: { orgId: testUser.currentOrg },
      });
      expect(batchEntities).toHaveLength(1);
      expect(batchEntities[0]).toMatchObject({
        orgId: testUser.currentOrg,
        createBy: testUser.userId,
        recordCount: 2,
        batchType: expect.any(Number),
        businessType: BatchBusinessTypeEnums.Diligence_ID,
      });

      // 验证结果中是否包含批次信息（第一处修改）
      const batchId = batchEntities[0].batchId;

      // 检查结果中的批次信息，使用可选链以避免undefined错误
      const batchResult1 = result1.batchEntities ? result1.batchEntities : [];
      const batchResult2 = result2.batchEntities ? result2.batchEntities : [];

      // 验证批次ID是否存在
      const hasBatchId1 = batchResult1.length === 0 || batchResult1.some((b) => b.batchId === batchId);
      const hasBatchId2 = batchResult2.length === 0 || batchResult2.some((b) => b.batchId === batchId);

      expect(hasBatchId1).toBeTruthy();
      expect(hasBatchId2).toBeTruthy();
    });

    it('缓存场景：使用缓存的历史尽调记录', async () => {
      // Given
      const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
      // 创建历史尽调记录
      const historicalDiligence = await entityManager.save(DiligenceHistoryEntity, {
        orgId: testUser.currentOrg,
        companyId,
        name: '测试公司',
        operator: testUser.userId,
        orgModelId: defaultRiskModelEntity.modelId,
        modelBranchCode: defaultRiskModelEntity.branchCode,
        product: testUser.currentProduct || 'default-product',
        score: 75,
        result: 1, // 假设 1 表示中风险
        details: null,
        snapshotId: 'test-snapshot-id',
        creditRate: 80,
        paid: 1,
        shouldUpdate: 0,
        createDate: new Date(), // 当前时间，确保在缓存时间范围内
      });

      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId];
      request.cacheHours = 24; // 使用缓存，24小时内的记录

      // When
      // 设置 spy 的默认返回值
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockResolvedValue(mockCompanyInfo);

      // 模拟 hitDimensionService 的 getDimensionHitResult 方法
      // 这个方法应该在缓存场景下不被调用，所以返回值不重要
      jest.spyOn(hitDimensionService, 'getDimensionHitResult').mockResolvedValue({
        allHits: [],
        timeCost: { total: 0 } as any,
      } as unknown as DimensionAllHitPO);

      // 模拟 snapshotService 的 prepareSnapshot 方法
      jest.spyOn(diligenceSnapshotService, 'prepareSnapshot').mockResolvedValue('test-cache-snapshot-id');

      const results = await evaluationService.getRiskList(testUser, request);

      // Then
      expect(results).toHaveLength(1);
      const result = results[0] as DiligenceResponse;
      expect(result).toMatchObject({
        id: historicalDiligence.id,
        companyId,
        name: historicalDiligence.name,
        score: historicalDiligence.score,
        result: historicalDiligence.result,
        cached: true, // 应该使用缓存
        paid: 0, // 应该被重置为0
        isFirstToModel: 0,
        isFirstToOrg: 0,
      });

      // 验证企查查接口被调用（为了确认公司信息）
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalled();

      // 缓存场景下不应该请求维度命中数据
      expect(hitDimensionService.getDimensionHitResult).not.toHaveBeenCalled();

      // 验证数据库中没有新增尽调记录
      const allRecords = await entityManager.find(DiligenceHistoryEntity, {
        where: { companyId, orgModelId: defaultRiskModelEntity.modelId },
      });
      expect(allRecords.length).toBe(1); // 应该只有一条记录
    });

    it('异常场景：企业类型不支持排查', async () => {
      // Given
      const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [defaultRiskModelEntity.modelId];

      // 设置企查查返回的企业类型为不支持的类型
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockResolvedValue({
        ...mockCompanyInfo,
        standardCode: ForbiddenStandardCode, // 使用实际的禁止企业类型常量
      });

      // 这些mock在异常情况下不应该被调用到
      jest.spyOn(hitDimensionService, 'getDimensionHitResult').mockResolvedValue({
        allHits: [],
        timeCost: { total: 0 } as any,
      } as unknown as DimensionAllHitPO);
      jest.spyOn(diligenceSnapshotService, 'prepareSnapshot').mockResolvedValue('test-error-snapshot-id');

      // When & Then - 期望抛出异常
      await expect(evaluationService.getRiskList(testUser, request)).rejects.toThrow();

      // 验证企查查接口被调用
      expect(companySearchService.companyDetailsQcc).toHaveBeenCalled();

      // 由于提前抛出异常，这些方法不应该被调用
      expect(hitDimensionService.getDimensionHitResult).not.toHaveBeenCalled();
      expect(diligenceSnapshotService.prepareSnapshot).not.toHaveBeenCalled();

      // 验证数据库中没有创建记录
      const records = await entityManager.find(DiligenceHistoryEntity, {
        where: { companyId, orgModelId: defaultRiskModelEntity.modelId },
      });
      expect(records.length).toBe(0);
    });
  });

  describe('getSpecificDiligenceResult 方法测试', () => {
    let defaultRiskModelEntity: RiskModelEntity;
    beforeEach(async () => {
      // 创建测试风险模型
      const modelName = `【集成测试】尽调模型-${Date.now()}`;
      defaultRiskModelEntity = await riskModelInitService.createHTFModel(testUser, modelName, testUser.currentOrg, RiskModelTypeEnums.GeneralRiskModel);
      // 更新模型状态为已启用
      await entityManager.update(RiskModelEntity, defaultRiskModelEntity.modelId, { status: DataStatusEnums.Enabled });
    });

    afterEach(async () => {
      if (defaultRiskModelEntity?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel, [defaultRiskModelEntity.modelId]);
      }
      await entityManager.delete(DiligenceHistoryEntity, { orgId: testOrgId });
    });

    it('成功获取特定尽调结果', async () => {
      // Given
      const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
      const historicalDiligence = await entityManager.save(DiligenceHistoryEntity, {
        orgId: testUser.currentOrg,
        companyId,
        name: '企查查科技股份有限公司',
        operator: testUserId,
        orgModelId: defaultRiskModelEntity.modelId,
        modelBranchCode: defaultRiskModelEntity.branchCode,
        product: ProductCodeEnums.Pro,
        score: 75,
        result: 1, // DueDiligenceResult.mediumRisk
        details: {
          totalScore: 75,
          result: 1, // DueDiligenceResult.mediumRisk
          modelType: RiskModelTypeEnums.GeneralRiskModel,
          groupMetricScores: [],
          vetoMetrics: [],
          originalHits: [],
          cost: { total: 100 },
        },
        snapshotId: 'test-snapshot-id',
        creditRate: 80,
        paid: 1,
        isFirstToModel: 0,
        isFirstToOrg: 0,
        shouldUpdate: 0,
        createDate: new Date(),
        updateDate: new Date(),
        createBy: testUserId,
        updateBy: testUserId,
      });

      // When
      const request = new GetDiligenceResultRequest();
      request.diligenceId = historicalDiligence.id;
      request.companyId = companyId;
      const result = await evaluationService.getSpecificDiligenceResult(testUser, request);

      // Then
      expect(result).toBeDefined();
      expect(result).toMatchObject({
        id: historicalDiligence.id,
        companyId,
        name: historicalDiligence.name,
        score: historicalDiligence.score,
        result: historicalDiligence.result,
        creditRate: historicalDiligence.creditRate,
      });
    });

    it('尝试获取不存在的尽调结果应该抛出异常', async () => {
      // Given - 一个不存在的尽调ID
      const nonExistentId = 999999;
      const companyId = 'f625a5b661058ba5082ca508f99ffe1b';

      // When & Then
      const request = new GetDiligenceResultRequest();
      request.diligenceId = nonExistentId;
      request.companyId = companyId;

      // 验证抛出异常
      await expect(evaluationService.getSpecificDiligenceResult(testUser, request)).rejects.toThrow();

      // 确认数据库中确实没有这条记录
      const record = await entityManager.findOne(DiligenceHistoryEntity, {
        where: { id: nonExistentId },
      });
      expect(record).toBeUndefined();
    });
  });

  describe('makeDiligenceForUpdate 方法测试', () => {
    let defaultRiskModelEntity: RiskModelEntity;
    let historicalDiligence: DiligenceHistoryEntity;
    beforeEach(async () => {
      // 创建测试风险模型
      const modelName = `【集成测试】尽调模型-${Date.now()}`;
      defaultRiskModelEntity = await riskModelInitService.createHTFModel(testUser, modelName, testUser.currentOrg, RiskModelTypeEnums.GeneralRiskModel);
      // 更新模型状态为已启用
      await entityManager.update(RiskModelEntity, defaultRiskModelEntity.modelId, { status: DataStatusEnums.Enabled });

      // 创建一条历史尽调记录
      historicalDiligence = await entityManager.save(DiligenceHistoryEntity, {
        orgId: testUser.currentOrg,
        companyId: 'f625a5b661058ba5082ca508f99ffe1b',
        name: '企查查科技股份有限公司',
        operator: testUserId,
        orgModelId: defaultRiskModelEntity.modelId,
        modelBranchCode: defaultRiskModelEntity.branchCode,
        product: ProductCodeEnums.Pro,
        score: 75,
        result: 1,
        details: {
          totalScore: 75,
          result: 1,
          modelType: RiskModelTypeEnums.GeneralRiskModel,
          groupMetricScores: [],
          vetoMetrics: [],
          originalHits: [],
          cost: { total: 100 },
        },
        snapshotId: 'test-snapshot-id',
        creditRate: 80,
        paid: 1,
        isFirstToModel: 0,
        isFirstToOrg: 0,
        shouldUpdate: 0,
        createDate: new Date(),
        updateDate: new Date(),
        createBy: testUserId,
        updateBy: testUserId,
      });
    });

    afterEach(async () => {
      if (defaultRiskModelEntity?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel, [defaultRiskModelEntity.modelId]);
      }
      if (historicalDiligence?.id) {
        await entityManager.delete(DiligenceHistoryEntity, { orgId: testOrgId, id: historicalDiligence.id });
      }
    });

    it('成功标记尽调记录为需要更新', async () => {
      // When - 调用更新方法
      await evaluationService.makeDiligenceForUpdate(testUser, historicalDiligence.companyId);

      // Then - 验证记录状态已更新
      const updatedDiligence = await entityManager.findOne(DiligenceHistoryEntity, {
        where: { id: historicalDiligence.id },
      });

      // 验证shouldUpdate字段已被设置为1
      expect(updatedDiligence).toBeDefined();
      expect(updatedDiligence.shouldUpdate).toBe(1);

      // 其他字段应保持不变
      expect(updatedDiligence.companyId).toBe(historicalDiligence.companyId);
      expect(updatedDiligence.orgModelId).toBe(historicalDiligence.orgModelId);
      expect(updatedDiligence.score).toBe(historicalDiligence.score);
    });
  });

  describe.skip('DEBUG 测试', () => {
    it(
      '11准入排查',
      async () => {
        const spy1 = jest.spyOn(diligenceSnapshotService.snapshotQueue, 'sendMessageV2').mockImplementation((msg, options) => {
          return diligenceSnapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.Diligence);
        });
        const testCompanyId = '7318ba09eb1adaf2ada2773ea03284a8';
        const testCompanyName = '南京中宝智慧科技服务有限公司';

        // 单元测试专用组织账户
        const testUser: PlatformUser = {
          loginUserId: 7973,
          userId: 217,
          currentProduct: ProductCodeEnums.Pro,
          currentOrg: 208,
          orgName: '测试组织',
        };

        // 执行排查
        const riskScoreResult = await evaluationService.getRiskList(testUser, {
          companyId: testCompanyId,
          companyName: testCompanyName,
          orgModelIds: [3801],
          cacheHours: 0,
        });
        await Bluebird.delay(10000);
        console.log(JSON.stringify(riskScoreResult));
        expect(riskScoreResult);
      },
      1000 * 1000,
    );

    it(
      '准入排查 test',
      async () => {
        const data: SingleCompanyDiligenceRequest = {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
          orgModelIds: [3801],
          cacheHours: 0,
        };

        try {
          // @ts-ignore
          testUser.currentOrg = 208;
          const res = (await evaluationService.getRiskList(testUser, data))[0];
          expect(res).not.toBeNull();
          // 第二次排查 不执行排查
          // @ts-ignore
          // res = (await evaluationService.getRiskList(testUser, data))[0];
          // expect(res.cached).toBeFalsy();
        } catch (e) {
          console.error(e);
          expect(e).toBeNull();
        }
      },
      300 * 1000,
    );

    it('getSpecificDiligenceResult', async () => {
      // testUser.currentOrg = 1;
      const result = await evaluationService.getSpecificDiligenceResult(testUser, {
        diligenceId: 50000001,
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      });

      expect(result);
    });

    it('test regex', async () => {
      const f = { totalHits: 10, description: '有2034万元，3条记录' };
      const regex = DescriptionRegex;
      const match = regex.exec(f.description);
      if (match) {
        const M: number = f.totalHits;
        f.description = f.description.replace(regex, M.toString() + '条记录');
      }
      expect(f.description).toBe('有2034万元，10条记录');
    });
  });

  describe('getRiskList 方法集成测试', () => {
    it('测试快照生成流程', async () => {
      // 准备测试数据
      const companyId = 'test-company-id';
      const companyName = '测试公司';
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyId;
      request.orgModelIds = [testRiskModel.modelId];

      // 模拟公司信息
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockResolvedValue({
        ...MockCompanyInfoConstant,
        KeyNo: companyId,
        Name: companyName,
      } as any);

      // 模拟命中结果
      const mockDimensionHits = [
        {
          strategyId: 123,
          strategyName: '测试策略',
          status: 1,
          dimensionKey: 'AntiFraud',
          dimensionName: '反欺诈',
          source: 'EnterpriseLib',
          totalHits: 1,
          description: '测试描述',
        },
        {
          strategyId: 456,
          strategyName: '测试策略2',
          status: 1,
          dimensionKey: 'LostCredit',
          dimensionName: '失信记录',
          source: 'CreditES',
          totalHits: 1,
          description: '测试描述2',
        },
      ];

      // 模拟维度命中结果
      jest.spyOn(hitDimensionService, 'getDimensionHitResult').mockResolvedValue({
        allHits: mockDimensionHits,
        timeCost: { total: 100 } as any,
      } as unknown as DimensionAllHitPO);

      // 模拟快照生成
      jest.spyOn(diligenceSnapshotService, 'prepareSnapshot').mockResolvedValue('test-snapshot-id');

      // 执行测试
      const result = await evaluationService.getRiskList(testUser, request);

      // 验证结果
      expect(result).toHaveLength(1);
      expect(result[0].companyId).toEqual(companyId);
      expect(result[0].name).toEqual(companyName);
      expect(result[0].orgModelId).toEqual(testRiskModel.modelId);

      // 验证快照创建
      expect(diligenceSnapshotService.prepareSnapshot).toHaveBeenCalled();

      // 验证数据库中创建了尽调记录
      const dbRecords = await entityManager.find(DiligenceHistoryEntity, {
        where: { companyId, orgModelId: testRiskModel.modelId },
      });
      expect(dbRecords.length).toBeGreaterThan(0);
    });

    it('测试多个风险模型同时尽调生成批次', async () => {
      // 准备多个模型
      const modelIds = [testRiskModel.modelId]; // 使用一个已定义的模型ID
      const companyInfo = {
        KeyNo: 'test-key-no',
        Name: '测试公司',
        standardCode: ['1000'], // 添加有效的标准代码，非禁止企业类型
      };

      // 模拟公司搜索服务
      jest.spyOn(companySearchService, 'companyDetailsQcc').mockResolvedValue(companyInfo as any);

      // 创建请求对象
      const request = new SingleCompanyDiligenceRequest();
      request.companyId = companyInfo.KeyNo;
      request.orgModelIds = modelIds;

      // 创建模拟的维度命中结果
      const mockHitResult = {
        allHits: [
          {
            strategyId: 123,
            strategyName: '测试策略',
            dimensionKey: 'AntiFraud',
            dimensionName: '反欺诈',
            status: 1,
            totalHits: 1,
            description: '测试描述',
          },
        ],
        timeCost: { total: 100 } as any, // 修复timeCost类型
      } as unknown as DimensionAllHitPO;

      // 模拟命中维度服务
      jest.spyOn(hitDimensionService, 'getDimensionHitResult').mockResolvedValue(mockHitResult);

      // 模拟快照生成
      jest.spyOn(diligenceSnapshotService, 'prepareSnapshot').mockResolvedValue('test-batch-snapshot-id');

      // 执行测试
      const results = await evaluationService.getRiskList(testUser, request);

      // 验证结果
      expect(results).toHaveLength(1);
      expect(results[0].companyId).toBe(companyInfo.KeyNo);

      // 验证hitDimensionService被正确调用
      expect(hitDimensionService.getDimensionHitResult).toHaveBeenCalledWith(
        testUser.currentOrg,
        companyInfo.KeyNo,
        companyInfo.Name,
        expect.objectContaining({ modelId: testRiskModel.modelId }),
        undefined,
      );

      // 验证数据库中是否创建了相应的尽调记录
      const diligenceRecords = await entityManager.find(DiligenceHistoryEntity, {
        where: { companyId: companyInfo.KeyNo, orgModelId: testRiskModel.modelId },
      });
      expect(diligenceRecords.length).toBeGreaterThan(0);

      // 验证快照服务被调用
      expect(diligenceSnapshotService.prepareSnapshot).toHaveBeenCalled();
    });
  });

  describe('getSpecificDiligenceResult 方法集成测试', () => {
    it('应该能获取特定的尽调结果', async () => {
      // 准备数据
      const diligenceId = 123456;
      const orgId = testUser.currentOrg;

      // 准备插入测试数据
      const nowDate = new Date();
      const testDiligenceHistory = {
        id: diligenceId,
        orgId,
        companyId: '123456',
        name: '测试公司',
        operator: testUser.userId,
        orgModelId: testRiskModel.modelId,
        modelBranchCode: testRiskModel.branchCode,
        product: ProductCodeEnums.Pro,
        score: 75,
        result: 1,
        details: {
          totalScore: 75,
          result: 1,
          modelType: RiskModelTypeEnums.GeneralRiskModel,
          cost: { total: 100 },
        },
        snapshotId: 'test-snapshot-id',
        creditRate: 80,
        paid: 1,
        isFirstToModel: 1,
        isFirstToOrg: 1,
        shouldUpdate: 0,
        createDate: nowDate,
        updateDate: nowDate,
        createBy: testUser.userId,
        updateBy: testUser.userId,
      };

      // 直接操作数据库插入测试数据
      await entityManager.save(DiligenceHistoryEntity, testDiligenceHistory);

      // 创建请求对象
      const request = new GetDiligenceResultRequest();
      request.diligenceId = diligenceId;
      request.companyId = '123456';

      // 执行测试
      const result = await evaluationService.getSpecificDiligenceResult(testUser, request);

      // 验证
      expect(result).toBeDefined();
      expect(result.id).toBe(diligenceId);
      expect(result.orgId).toBe(orgId);

      // 清理测试数据
      await entityManager.delete(DiligenceHistoryEntity, { id: diligenceId });
    });

    it('当尝试访问其他组织的尽调结果时应抛出异常', async () => {
      // 准备数据
      const diligenceId = 123457;
      const orgId = testUser.currentOrg;
      const otherUser = { ...testUser, currentOrg: orgId + 1 }; // 创建一个不同组织的用户

      // 准备插入测试数据
      const nowDate = new Date();
      const testDiligenceHistory = {
        id: diligenceId,
        orgId, // 实际记录属于测试用户组织
        companyId: '123456',
        name: '测试公司',
        operator: testUser.userId,
        orgModelId: testRiskModel.modelId,
        modelBranchCode: testRiskModel.branchCode,
        product: ProductCodeEnums.Pro,
        score: 75,
        result: 1,
        details: {
          totalScore: 75,
          result: 1,
          modelType: RiskModelTypeEnums.GeneralRiskModel,
          cost: { total: 100 },
        },
        snapshotId: 'test-snapshot-id',
        creditRate: 80,
        paid: 1,
        isFirstToModel: 1,
        isFirstToOrg: 1,
        shouldUpdate: 0,
        createDate: nowDate,
        updateDate: nowDate,
        createBy: testUser.userId,
        updateBy: testUser.userId,
      };

      // 直接操作数据库插入测试数据
      await entityManager.save(DiligenceHistoryEntity, testDiligenceHistory);

      // 创建请求对象
      const request = new GetDiligenceResultRequest();
      request.diligenceId = diligenceId;
      request.companyId = '123456';

      // 尝试使用不同组织ID访问该记录，应该抛出异常
      await expect(evaluationService.getSpecificDiligenceResult(otherUser, request)).rejects.toThrow();

      // 清理测试数据
      await entityManager.delete(DiligenceHistoryEntity, { id: diligenceId });
    });
  });
});
